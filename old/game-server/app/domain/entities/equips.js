/**
 * Created by aaa on 2015/5/27.
 */

var logger = require('pomelo-logger').getLogger("pomelo", __filename);;
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var dataApi = require('../../util/dataApi');
var utils = require('../../util/utils');
var calc = require('../../util/calc');
var Code = require('../../../../shared/code');

var Equips = function(playerId) {
    this.playerId = playerId;
    this.allEquips = [];
};

util.inherits(Equips, EventEmitter);

module.exports = Equips;

Equips.prototype.initByDB = function(doc) {
    this.playerId = doc.playerId;
    this.allEquips = doc.allEquips || [];
};

Equips.prototype.initByConfig = function() {

};

Equips.prototype.toJSONforDB = function() {
    var equips = {
        playerId: this.playerId,
        allEquips: this.allEquips
    };
    return equips;
};

Equips.prototype.toJSONforClient = function() {
    var equipsData = [];
    for(var id in this.allEquips) {
        if(!this.allEquips[id]["youxibiHide"] || this.allEquips[id]["youxibiHide"] === 0) {
            equipsData.push(this.allEquips[id]);
        }
    }
    return JSON.stringify(equipsData);
};

Equips.prototype.addEquip = function (resId, star) {
    var max = this.allEquips.length;
    var equipment = this.newEquipment(max, resId, star);
    this.allEquips.push(equipment);
    return equipment["entityId"];
};

Equips.prototype.newEquipment = function (entityId, resId, star) {
    var equipment = {};
    equipment["entityId"] = entityId;
    equipment["resId"] = resId;
    equipment["level"] = 0;
    equipment["star"] = star;
    var mainAttId = this.getMainAttId(resId, star);
    var mainAtt = this.randomMainAtt(mainAttId);
    equipment["mainAttIndex"] = mainAtt[0];
    equipment["mainAttValue"] = mainAtt[1];
    equipment["color"] = this.randomColor(mainAttId);
    var addAtt = this.randomAddAtt(mainAttId, equipment["color"]);
    equipment["addAttIndex"] = addAtt[0];
    equipment["addAttValue"] = addAtt[1];
    equipment["addAttLevel"] = [];
    for(var n=0;n<equipment["addAttIndex"].length;n++) {
        equipment["addAttLevel"].push(1);
    }
    equipment["heroEntityId"] = -1;
    equipment["youxibiHide"] = 0;
    logger.debug('new equipment name,data:',dataApi.allData.data["Equipment"][resId]["DesignerName"],equipment);
    return equipment;
};

Equips.prototype.refreshAtt = function (entityId) {
    var resId = this.allEquips[entityId]["resId"];
    var star = this.allEquips[entityId]["star"];
    var mainAttId = this.getMainAttId(resId, star);
    logger.debug('resId, mainAttId',resId,mainAttId);
    this.allEquips[entityId]["level"] = 0;
    var mainAtt = this.randomMainAtt(mainAttId);
    this.allEquips[entityId]["mainAttIndex"] = mainAtt[0];
    this.allEquips[entityId]["mainAttValue"] = mainAtt[1];
    this.allEquips[entityId]["color"] = this.randomColor(mainAttId);
    var addAtt = this.randomAddAtt(mainAttId, this.allEquips[entityId]["color"]);
    this.allEquips[entityId]["addAttIndex"] = addAtt[0];
    this.allEquips[entityId]["addAttValue"] = addAtt[1];
    this.allEquips[entityId]["addAttLevel"] = [];
    for(var n=0;n<this.allEquips[entityId]["addAttIndex"].length;n++) {
        this.allEquips[entityId]["addAttLevel"].push(1);
    }
};

Equips.prototype.getMainAttId = function(resId, star) {
    return dataApi.allData.data["Equipment"][resId]["EqStar"][star-1];
};

Equips.prototype.randomColor = function (resId) {
    var data = dataApi.allData.data["MainAtt"][resId]["Odds_Color"];
    var randNum = utils.random(1, 100);
    var below = 0;
    for(var n=0;n<data.length;n++) {
        below += data[n];
        if (below >= randNum) {
            return n+1;
        }
    }
    return 1;
};

Equips.prototype.randomMainAtt = function (resId) {
    var dataList = dataApi.allData.data["MainAtt"][resId]["AttList"];
    var radioData = dataApi.allData.data["MainAtt"][resId]["Odds_AttList"];
    var needNum = dataApi.allData.data["MainAtt"][resId]["MainAttNum"];
    return calc.drawList(dataList, radioData, needNum, true);
};

Equips.prototype.randomAddAtt = function (mainAttId, color) {
    var addAttId = dataApi.allData.data["MainAtt"][mainAttId]["AddAtt"];
    var dataList = dataApi.allData.data["AddAtt"][addAttId]["SuitAttList"];
    var needNum = color - 1;
    var radioList = [];
    for(var n=0;n<dataList.length;n++) {
        if(dataList[n] > 0) {
            radioList.push(1);
        }
        else{
            radioList.push(0);
        }
    }
    return calc.drawList(dataList,radioList,needNum,true);
};

Equips.prototype.setHeroId = function (entityId, heroEntityId) {
    this.allEquips[entityId]["heroEntityId"] = heroEntityId;
};

Equips.prototype.strengthen = function(entityId) {
    this.allEquips[entityId]["level"] += 1;
    var mainAttId = this.getMainAttId(this.allEquips[entityId]["resId"], this.allEquips[entityId]["star"]);
    //1.增加基础属性
    for(var n=0;n<this.allEquips[entityId]["mainAttIndex"].length;n++) {
        var index = this.allEquips[entityId]["mainAttIndex"][n];
        logger.debug('mainAtt ---------------',index, dataApi.allData.data["MainAtt"][mainAttId]["Up_AttList"][index]);
        this.allEquips[entityId]["mainAttValue"][n] += dataApi.allData.data["MainAtt"][mainAttId]["Up_AttList"][index];
    }
    //2.每3次变更附加属性。变更颜色大于装备颜色，则附加属性条数增加，否则在现有属性中抽取一条加强。
    if(this.allEquips[entityId]["level"] % 3 == 0) {
        var limitNum = this.allEquips[entityId]["color"] - 1;
        var times = this.allEquips[entityId]["level"] / 3;
        var addAttId = dataApi.allData.data["MainAtt"][mainAttId]["AddAtt"];
        if((times > 1 && limitNum < 4) || limitNum == 0) {
            var dataList = dataApi.allData.data["AddAtt"][addAttId]["SuitAttList"];
            var need = 1;
            var radioList = [];
            for(var n=0;n<dataList.length;n++) {
                if(dataList[n] > 0) {
                    radioList.push(1);
                }
                else{
                    radioList.push(0);
                }
                var isHave = 0;
                for(var m=0;m<this.allEquips[entityId]["addAttIndex"].length;m++) {
                    if (n == this.allEquips[entityId]["addAttIndex"][m]) {
                        isHave = 1;
                        break;
                    }
                }
                if(isHave == 1) {
                    radioList[n] = 0;
                }
            }
            var ret = calc.drawList(dataList, radioList, need, false);
            //logger.debug('ret ---------------',dataList, radioList, this.allEquips[entityId]["addAttIndex"], ret);
            this.allEquips[entityId]["addAttIndex"].push(ret[0][0]);
            this.allEquips[entityId]["addAttValue"].push(ret[1][0]);
            this.allEquips[entityId]["addAttLevel"].push(1);
            this.allEquips[entityId]["color"] += 1;
            //logger.debug('xxxx ---------',this.allEquips[entityId]["addAttIndex"]);
        }else {
            var rand = calc.randRange(0, limitNum-1);
            var index = this.allEquips[entityId]["addAttIndex"][rand];
            var nextAttId = addAttId + this.allEquips[entityId]["addAttLevel"][rand];
            var afterValue = dataApi.allData.data["AddAtt"][nextAttId]["SuitAttList"][index];
            this.allEquips[entityId]["addAttValue"][rand] = afterValue;
            this.allEquips[entityId]["addAttLevel"][rand] += 1;
            logger.debug('after --------------',rand, index, nextAttId, afterValue);
        }
    }
    return {code:Code.OK, isSuccess: 1};
};

Equips.prototype.upStar = function (entityId) {
    this.allEquips[entityId]["star"] += 1;
    //重新刷新属性
    this.refreshAtt(entityId);
    return {code:Code.OK, isSuccess:1};
};

Equips.prototype.removeOne = function (entityId) {
    this.allEquips.splice(entityId, 1);
    for(var n=entityId;n<this.allEquips.length;n++) {
        this.allEquips[n]["entityId"] -= 1;
    }
};
