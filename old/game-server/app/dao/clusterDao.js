/**
 * Created by aaa on 2015/5/12.
 */

var logger = require('pomelo-logger').getLogger("pomelo", __filename);;
var Code = require('../../../shared/code');
var Utils = require('../util/utils');
var CommonEnum = require('../../../shared/enum');
var TimeUtil = require('../util/timeUtils');
var clusterConfig = require('../../config/cluster.json');
var async = require('async');
//var ObjectId = require('mongodb').ObjectId;

//集群数据访问接口
module.exports.create = function(db){
    return new ClusterDao(db);
};

var ClusterDao = function(db){
    this.db = db;
};

//外部接口函数
ClusterDao.prototype.findOrCreate = function(msg, cb) {
    this.db.collection("account", function (err, collection) {
        collection.findOne({
            oid: msg.openId
        }, function(err, doc) {
            if(!!err){
                logger.debug('get account fail, openId:',msg.openId);
                return cb(Code.FAIL);
            }
            if(doc){
                logger.debug('find the account by openId:', msg.openId);
                return cb(Code.OK, doc);
            }else{
                //new uid
                msg.uid = Utils.syncCreateUid();
                collection.insertOne({
                    oid: msg.openId,
                    uid: msg.uid,
                    gid: msg.gid,
                    name: msg.name,
                    level: 0,
                    fansCount: 0,
                    actualStrength: 0
                }, {w: 1}, function(err){
                    if(!!err){
                        logger.debug('insert new account fail, openId:', msg.openId);
                        return cb(Code.FAIL);
                    }
                    cb(Code.OK, msg);
                });
            }
        });
    });
};

ClusterDao.prototype.getAllGlobalSystem = function(cb)
{
	let collection = this.db.collection("globalSystem");
	collection.find().toArray(function(err,  doc) {
		if (!!err)
		{
			logger.debug('ClusterDao all player account err', err);
			return cb(err);
		}
		return cb(null, doc);
	});
};

ClusterDao.prototype.updateGlobalSystemByTypeId = function(typeId, obj, cb){
    let collection = this.db.collection("globalSystem");
    collection.findOne({
        typeId: typeId
    }, function(err, doc) {
        if(!!err){
            logger.debug('updateGlobalSystemByTypeId: get account fail, typeId:', typeId);
            return cb(err);
        }

        if(!!doc){
            collection.updateOne({typeId: typeId}, {
                $set: { data: obj.data, lastUpdateTime: obj.lastUpdateTime,}
            },{upsert:true, w: 1}).then(function(result) {
                    return cb(null);
            });
        }else{
            //new uid
            collection.insertOne({
                typeId: obj.typeId,
                data: obj.data,
                lastUpdateTime: obj.lastUpdateTime,
            }, {w: 1}, function(err){
                if(!!err){
                    logger.debug('insert new global system fail, openId:', msg.openId);
                    return cb(err);
                }
                cb(null);
            });
        }
    });
};

ClusterDao.prototype.getAllActivity = function(cb)
{
	let collection = this.db.collection("Activity");
	collection.find().toArray(function(err,  doc) {
		if (!!err)
		{
			logger.debug('ClusterDao all player account err', err);
			return cb(err);
		}
		return cb(null, doc);
	});
};

ClusterDao.prototype.updateActivityById = function(Id, obj, cb){
    let collection = this.db.collection("Activity");
    collection.findOne({
        Id: Id
    }, function(err, doc) {
        if(!!err){
            logger.debug('updateActivityById: get account fail, Id:', Id);
            return cb(err);
        }

        if(!!doc){
            collection.updateOne({Id: Id}, {
                $set: { Id: obj.Id, timeType:obj.timeType, startTime: obj.startTime, endTime: obj.endTime, state: obj.state, updateTime: obj.updateTime,}
            },{upsert:true, w: 1}).then(function(result) {
                    return cb(null);
            });
        }else{
            //new uid
            collection.insertOne({
                Id: obj.Id,
                timeType: obj.timeType,
                startTime: obj.startTime,
                endTime: obj.endTime,
                state: obj.state,
                updateTime: obj.updateTime,
            }, {w: 1}, function(err){
                if(!!err){
                    return cb(err);
                }

                cb(null);
                return;
            });
        }
    });
};

ClusterDao.prototype.getOneServerAccountNum = function(gid, cb)
{
    let collection = this.db.collection("account");
    collection.find({gid: gid}).count(function (err, result) {
        if(!!err)
        {
            logger.warn('getOneServerAccountNum. err', err);
            return cb(err, result);
        }
        logger.debug("getOneServerAccountNum gid, accountNum: ", gid, result);
        return cb(null, result);
    })
};

ClusterDao.prototype.getDeemCode = function(CodeID, cb)
{
	let collection = this.db.collection("deemCode");
	collection.findOne({CodeID: CodeID}, function(err, doc) {
		if (!!err)
		{
			return cb(err);
		}
		return cb(null, doc);
	});
};

ClusterDao.prototype.updateDeemCodeUseCount = function (playerId, CodeID, useCount, useTime, cb) {
	let collection = this.db.collection("deemCode");
	collection.findOne({CodeID: CodeID}, function (err, doc) {
		if(!!err) {
			return cb(err);
		}

		if(!!doc) 
		{
			if(!doc.UseCount) {
				doc.UseCount = 0;
            }
            if (!doc.UseInfoList)
            {
                doc.UseInfoList = [];
            }
            let obj = {
                playerId: playerId,
                useTime: useTime,
            }
            doc.UseInfoList.push(obj);
			doc.UseCount = useCount;
			collection.updateOne({
				CodeID: CodeID
			},{
				$set: {UseCount: doc.UseCount, UseInfoList: doc.UseInfoList}
			}, function (err) {
				if(!!err){
					logger.warn('updateOneMatchFightTimes err',err);
					return cb(Code.FAIL);
				}
	
				return cb(null);
			});
		}else {
			return cb("not found CodeID: ", CodeID, useCount);
		}
	});
};

ClusterDao.prototype.findOrCreateChatMsgCache = function (cb) {
    let collection = this.db.collection("common");
    collection.findOne({
        id: CommonEnum.COMMON_DB_ID.CHAT_MSG_CACHE
    }, function(err, doc) {
        if(!!err){
            logger.debug('findOrCreateChatMsgCache: get msgCache fail, id:', CommonEnum.COMMON_DB_ID.CHAT_MSG_CACHE);
            return cb(err);
        }
        if(!!doc){
            return cb(null, doc);
        }else{
            collection.insertOne({
                id: CommonEnum.COMMON_DB_ID.CHAT_MSG_CACHE
            }, {w: 1}, function(err){
                if(!!err){
                    return cb(err);
                }
                cb(null);
            });
        }
    });
};

//记录MVP球员
ClusterDao.prototype.recordBuyMvpHero = function (msg, cb) {
    let collection = this.db.collection("mvpFootball");
    collection.findOne({
        mvpId: 1
    }, function(err, doc) {
        if(!!err){
            logger.debug('recordBuyMvpHero:', msg.itemInfo);
            return cb(Code.FAIL);
        }

        if(!!doc){
            if(!doc.buyList) {
                doc.buyList = [];
            }

            doc.buyList.push(msg.itemInfo);
            //更新数据
            collection.updateOne({
                mvpId: 1
            },{
                $set : { buyList: doc.buyList }
            }, function (err) {
                if(!!err){
                    logger.error('recordBuyMvpHero-updateOne err');
                    return cb(Code.FAIL);
                }
                return cb(Code.OK);
            });
        } else {
            let buyList = [];
            buyList.push(msg.itemInfo);
            collection.insertOne({
                mvpId: 1,
                mvpHeroId: 0,         //Mvp球员id
                buyList: buyList
            }, {w: 1}, function(err){
                if(!!err){
                    return cb(Code.FAIL);
                }
                cb(Code.OK);
            });
        }
    });
};

//更新Mvp发送奖励状态
ClusterDao.prototype.updateMvpSendRewardStatus = function (msg, cb) {
    let collection = this.db.collection("mvpFootball");
    collection.findOne({
        mvpId: 1
    }, function(err, doc) {
        if(!!err){
            logger.debug('updateMvpSendRewardStatus:');
            return cb(Code.FAIL);
        }

        if(!!doc){
            for(let i in msg.playerList) {
                for(let j = 0; j < doc.buyList.length; ++j) {
                    if(msg.playerList[i].playerId === doc.buyList[j].playerId
                        && msg.playerList[i].buyTime === doc.buyList[j].buyTime) {
                        doc.buyList[j].isSend = 1;
                    }
                }
            }

            //更新数据
            collection.updateOne({
                mvpId: 1
            },{
                $set : { buyList: doc.buyList }
            }, function (err) {
                if(!!err){
                    logger.error('updateMvpSendRewardStatus err');
                    return cb(Code.FAIL);
                }
                return cb(Code.OK);
            });
        } else {
            cb(Code.FAIL)
        }
    });
};

//获取购买Mvp球员的人
ClusterDao.prototype.getBuyMvpHeroPlayer = function (msg, cb) {
    let collection = this.db.collection("mvpFootball");
    collection.findOne({
        mvpId: 1
    }, function(err, doc) {
        if(!!err){
            logger.debug('checkIsSendMvpHeroReward:');
            return cb(Code.FAIL, 0, []);
        }

        if(!!doc){
            return cb(Code.OK, doc.mvpHeroId, doc.buyList);
        } else {
            cb(Code.FAIL, 0, [])
        }
    });
};

//获取Mvp球员
ClusterDao.prototype.getMvpHeroId = function (msg, cb) {
    let collection = this.db.collection("mvpFootball");
    collection.findOne({
        mvpId: 1
    }, function(err, doc) {
        if(!!err){
            logger.debug('getMvpHeroId:');
            return cb(Code.FAIL, 0);
        }

        if(!!doc){
            return cb(Code.OK, doc.mvpHeroId);
        } else {
            cb(Code.OK, 0)
        }
    });
};

//设置Mvp球员
ClusterDao.prototype.setMvpHeroId = function (msg, cb) {
    let collection = this.db.collection("mvpFootball");
    collection.findOne({
        mvpId: 1
    }, function(err, doc) {
        if(!!err){
            logger.debug('setMvpHeroId:');
            return cb(Code.FAIL, 0);
        }

        if(!!doc){
            //更新数据
            collection.updateOne({
                mvpId: 1
            },{
                $set : { mvpHeroId: msg.mvpHeroId }
            }, function (err) {
                if(!!err){
                    logger.error('setMvpHeroId err');
                    return cb(Code.FAIL);
                }
                return cb(Code.OK);
            });
        } else {
            let buyList = [];
            collection.insertOne({
                mvpId: 1,
                mvpHeroId: msg.mvpHeroId,         //Mvp球员id
                buyList: buyList
            }, {w: 1}, function(err){
                if(!!err){
                    return cb(Code.FAIL);
                }
                return cb(Code.OK);
            });
        }
    });
};


//最佳11人
ClusterDao.prototype.addBestFootballOrder = function (orderInfo, cb) {
    let collection = this.db.collection("bestFootball");
    collection.findOne({
        openId: orderInfo.openId
    }, function(err, doc) {
        if(!!err){
            logger.debug('findOrCreateBestFootballOrder:', orderInfo.orderId);
            return cb(Code.FAIL);
        }

        if(!!doc){
            if(!doc.orderList) {
                doc.orderList = [];
            }

            let isHave = false;
            let isDraw = false;
            for(let i = 0; i < doc.orderList.length; ++i) {
                if(doc.orderList[i].orderId === orderInfo.orderId) {
                    isHave = true;
                    break;
                }else if(TimeUtil.isToday(doc.orderList[i].orderTime)){
                    isDraw = true;
                    break;
                }
            }

            let isInWhiteList = clusterConfig.white_list_best_eleven[orderInfo.openId];

            //订单已存在
            if(isHave && (!isInWhiteList)) {
                return cb(Code.BEST_ELEVEN.SAME_ORDER_SEND);
            }

            //当天已抽过
            if(isDraw && (!isInWhiteList)) {
                return cb(Code.BEST_ELEVEN.TODAY_IS_DRAW);
            }

            let info = {
                orderTime: TimeUtil.now(),        //记录时间
                orderId: orderInfo.orderId,       //订单Id
                itemId: orderInfo.itemId,         //物品Id
                sendTime: 0,                      //发货时间
                status: 1,                        //1 已记录  2已发货
            };
            doc.orderList.push(info);
            //更新数据
            collection.updateOne({
                openId: orderInfo.openId
            },{
                $set : {orderList: doc.orderList}
            }, function (err) {
                if(!!err){
                    logger.warn('updateOne err', orderInfo.orderId);
                    return cb(Code.FAIL);
                }
                return cb(Code.OK);
            });
        } else {
            let orderList = [];
            let info = {
                orderTime: TimeUtil.now(),        //记录时间
                orderId: orderInfo.orderId,       //订单Id
                itemId: orderInfo.itemId,         //物品Id
                sendTime: 0,                      //发货时间
                status: 1,                        //1 已记录  2已发货
            };
            orderList.push(info);
            collection.insertOne({
                openId: orderInfo.openId,
                orderList: orderList
            }, {w: 1}, function(err){
                if(!!err){
                    return cb(Code.FAIL);
                }
                cb(Code.OK);
            });
        }
    });
};

//老虎机
ClusterDao.prototype.addTurntableOrder = function (orderInfo, cb) {
    let collection = this.db.collection("turntable");
    collection.findOne({
        openId: orderInfo.openId
    }, function(err, doc) {
        if(!!err){
            logger.debug('findOrCreateTurntableOrder:', orderInfo.orderId);
            return cb(Code.FAIL);
        }

        if(!!doc){
            if(!doc.orderList) {
                doc.orderList = [];
            }

            let isHave = false;
            let isDraw = false;
            for(let i = 0; i < doc.orderList.length; ++i) {
                if(doc.orderList[i].orderId === orderInfo.orderId) {
                    isHave = true;
                    break;
                }else if(TimeUtil.isToday(doc.orderList[i].orderTime)){
                    isDraw = true;
                    break;
                }
            }

            //订单已存在
            if(isHave) {
                return cb(Code.BEST_ELEVEN.SAME_ORDER_SEND);
            }

            //当天已抽过
            if(isDraw) {
                return cb(Code.BEST_ELEVEN.TODAY_IS_DRAW);
            }

            let info = {
                orderTime: TimeUtil.now(),        //记录时间
                orderId: orderInfo.orderId,       //订单Id
                itemId: orderInfo.itemId,         //物品Id
                sendTime: 0,                      //发货时间
                status: 1,                        //1 已记录  2已发货
            };
            doc.orderList.push(info);
            //更新数据
            collection.updateOne({
                openId: orderInfo.openId
            },{
                $set : {orderList: doc.orderList}
            }, function (err) {
                if(!!err){
                    logger.warn('updateOne err', orderInfo.orderId);
                    return cb(Code.FAIL);
                }
                return cb(Code.OK);
            });
        } else {
            let orderList = [];
            let info = {
                orderTime: TimeUtil.now(),        //记录时间
                orderId: orderInfo.orderId,       //订单Id
                itemId: orderInfo.itemId,         //物品Id
                sendTime: 0,                      //发货时间
                status: 1,                        //1 已记录  2已发货
            };
            orderList.push(info);
            collection.insertOne({
                openId: orderInfo.openId,
                orderList: orderList
            }, {w: 1}, function(err){
                if(!!err){
                    return cb(Code.FAIL);
                }
                cb(Code.OK);
            });
        }
    });
};

//主站抽奖
ClusterDao.prototype.addMainDrawOrder = function (orderInfo, cb) {
    let collection = this.db.collection("mainDraw");
    let account = this.db.collection("account");

    async.waterfall([
        function (callback) {
            //检查玩家是否有游戏角色
            account.findOne({
                oid: orderInfo.openId
            },function (err, doc) {
                if(!!err){
                    logger.debug('findOrCreateMainDrawOrder:', orderInfo.orderId, orderInfo.openId);
                    return cb(Code.FAIL);
                }
                if(!doc) {
                    logger.debug('inexistence account openId:', orderInfo.orderId, orderInfo.openId);
                    return cb(Code.BEST_ELEVEN.ACCOUNT_IS_NOT);
                }
                callback(null);
            });
        },
        function (callback) {
            collection.findOne({
                openId: orderInfo.openId
            }, function(err, doc) {
                //logger.error("添加订单");
                if(!!err){
                    logger.debug('findOrCreateMainDrawOrder:', orderInfo.orderId);
                    return cb(Code.FAIL);
                }

                if(!!doc){
                    if(!doc.orderList) {
                        doc.orderList = [];
                    }

                    let isHave = false;
                    for(let i = 0; i < doc.orderList.length; ++i) {
                        if(doc.orderList[i].orderId === orderInfo.orderId) {
                            isHave = true;
                            break;
                        }
                    }

                    //订单已存在
                    if(isHave) {
                        return cb(Code.BEST_ELEVEN.SAME_ORDER_SEND);
                    }

                    let info = {
                        orderTime: TimeUtil.now(),        //记录时间
                        orderId: orderInfo.orderId,       //订单Id
                        itemId: orderInfo.itemId,         //物品Id
                        sendTime: 0,                      //发货时间
                        status: 1,                        //1 已记录  2已发货
                    };

                    doc.orderList.push(info);
                    //更新数据
                    collection.updateOne({
                        openId: orderInfo.openId
                    },{
                        $set : {orderList: doc.orderList}
                    }, function (err) {
                        if(!!err){
                            logger.warn('updateOne err', orderInfo.orderId);
                            return cb(Code.FAIL);
                        }
                        return cb(Code.OK);
                    });
                } else {
                    let orderList = [];
                    let info = {
                        orderTime: TimeUtil.now(),        //记录时间
                        orderId: orderInfo.orderId,       //订单Id
                        itemId: orderInfo.itemId,         //物品Id
                        sendTime: 0,                      //发货时间
                        status: 1,                        //1 已记录  2已发货
                    };

                    orderList.push(info);
                    collection.insertOne({
                        openId: orderInfo.openId,
                        orderList: orderList
                    }, {w: 1}, function(err){
                        if(!!err){
                            return cb(Code.FAIL);
                        }
                        return cb(Code.OK);
                    });
                }
            });
        }
        ],
        function (err) {

    });
};

ClusterDao.prototype.getBestFootballReward = function (openId, cb) {
    let collection = this.db.collection("bestFootball");
    collection.findOne({
        openId: openId
    }, function(err, doc) {
        if (!!err) {
            logger.debug('getBestFootballReward:', openId);
            return cb(Code.FAIL);
        }

        if (!!doc) {

            if(!doc.orderList) {
                doc.orderList = [];
            }

            let orderList = [];
            if(doc.orderList.length < 1) {
                return cb(null, orderList)
            }

            let index = 0;
            for(let i = 0, len = doc.orderList.length; i < len; ++i) {
                if(doc.orderList[i].status !== 2) {
                    orderList[index] = {};
                    orderList[index].orderId = doc.orderList[i].orderId;
                    orderList[index].itemId = doc.orderList[i].itemId;
                    orderList[index].num = 1;
                    index++;
                }
            }
            return cb(null, orderList)
        }else {
            //没有玩家数据
            return cb(Code.FAIL);
        }
    });
};

ClusterDao.prototype.getBestTurntableReward = function (openId, cb) {
    let collection = this.db.collection("turntable");
    collection.findOne({
        openId: openId
    }, function(err, doc) {
        if (!!err) {
            logger.debug('getBestTurntableReward:', openId);
            return cb(Code.FAIL);
        }

        if (!!doc) {

            if(!doc.orderList) {
                doc.orderList = [];
            }

            let orderList = [];
            if(doc.orderList.length < 1) {
                return cb(null, orderList)
            }

            let index = 0;
            for(let i = 0, len = doc.orderList.length; i < len; ++i) {
                if(doc.orderList[i].status !== 2) {
                    orderList[index] = {};
                    orderList[index].orderId = doc.orderList[i].orderId;
                    orderList[index].itemId = doc.orderList[i].itemId;
                    orderList[index].num = 1;
                    index++;
                }
            }
            return cb(null, orderList)
        }else {
            //没有玩家数据
            return cb(Code.FAIL);
        }
    });
};
ClusterDao.prototype.getBestMainDrawReward = function (openId, cb) {
    let collection = this.db.collection("mainDraw");
    collection.findOne({
        openId: openId
    }, function(err, doc) {
        if (!!err) {
            logger.debug('getBestMainDrawReward:', openId);
            return cb(Code.FAIL);
        }

        if (!!doc) {

            if(!doc.orderList) {
                doc.orderList = [];
            }

            let orderList = [];
            if(doc.orderList.length < 1) {
                return cb(null, orderList)
            }

            let index = 0;
            for(let i = 0, len = doc.orderList.length; i < len; ++i) {
                if(doc.orderList[i].status !== 2) {
                    orderList[index] = {};
                    orderList[index].orderId = doc.orderList[i].orderId;
                    orderList[index].itemId = doc.orderList[i].itemId;
                    orderList[index].num = 1;
                    index++;
                }
            }
            return cb(null, orderList)
        }else {
            //没有玩家数据
            return cb(Code.FAIL);
        }
    });
};
ClusterDao.prototype.updateBestFootballRewardStatus = function (msg, cb) {
    let collection = this.db.collection("bestFootball");
    collection.findOne({
        openId: msg.openId
    }, function(err, doc) {
        if (!!err) {
            return cb(Code.FAIL);
        }
        if (!!doc) {
            if(!doc.orderList) {
                doc.orderList = [];
            }

            if(doc.orderList.length < 1) {
                return cb(null);
            }

            for(let j = 0, lens = msg.orderList.length; j < lens; ++j) {
                for(let i = 0, len = doc.orderList.length; i < len; ++i) {
                    if(msg.orderList[j].orderId === doc.orderList[i].orderId && doc.orderList[i].status !== 2) {
                        doc.orderList[i].status = 2;
                        doc.orderList[i].sendTime = msg.sendTime;
                        break;
                    }
                }
            }

            collection.updateOne({
                openId: msg.openId
            },{
                $set : {orderList: doc.orderList}
            }, function (err) {
                if(!!err){
                    logger.warn('updateBestFootballRewardStatus err', msg.orderId);
                    return cb(Code.FAIL);
                }
                return cb(null);
            });
        }else {
            //没有玩家数据
            return cb(Code.FAIL);
        }
    });
};

ClusterDao.prototype.updateBestTurntableRewardStatus = function (msg, cb) {
    let collection = this.db.collection("turntable");
    collection.findOne({
        openId: msg.openId
    }, function(err, doc) {
        if (!!err) {
            return cb(Code.FAIL);
        }
        if (!!doc) {
            if(!doc.orderList) {
                doc.orderList = [];
            }

            if(doc.orderList.length < 1) {
                return cb(null);
            }

            for(let j = 0, lens = msg.orderList.length; j < lens; ++j) {
                for(let i = 0, len = doc.orderList.length; i < len; ++i) {
                    if(msg.orderList[j].orderId === doc.orderList[i].orderId && doc.orderList[i].status !== 2) {
                        doc.orderList[i].status = 2;
                        doc.orderList[i].sendTime = msg.sendTime;
                        break;
                    }
                }
            }

            collection.updateOne({
                openId: msg.openId
            },{
                $set : {orderList: doc.orderList}
            }, function (err) {
                if(!!err){
                    logger.warn('updateBestTurntableRewardStatus err', msg.orderId);
                    return cb(Code.FAIL);
                }
                return cb(null);
            });
        }else {
            //没有玩家数据
            return cb(Code.FAIL);
        }
    });
};
ClusterDao.prototype.updateBestMainDrawRewardStatus = function (msg, cb) {
    let collection = this.db.collection("mainDraw");
    collection.findOne({
        openId: msg.openId
    }, function(err, doc) {
        if (!!err) {
            return cb(Code.FAIL);
        }
        if (!!doc) {
            if(!doc.orderList) {
                doc.orderList = [];
            }

            if(doc.orderList.length < 1) {
                return cb(null);
            }

            for(let j = 0, lens = msg.orderList.length; j < lens; ++j) {
                for(let i = 0, len = doc.orderList.length; i < len; ++i) {
                    if(msg.orderList[j].orderId === doc.orderList[i].orderId && doc.orderList[i].status !== 2) {
                        doc.orderList[i].status = 2;
                        doc.orderList[i].sendTime = msg.sendTime;
                        break;
                    }
                }
            }

            collection.updateOne({
                openId: msg.openId
            },{
                $set : {orderList: doc.orderList}
            }, function (err) {
                if(!!err){
                    logger.warn('updateBestMainDrawRewardStatus err', msg.orderId);
                    return cb(Code.FAIL);
                }
                return cb(null);
            });
        }else {
            //没有玩家数据
            return cb(Code.FAIL);
        }
    });
};
ClusterDao.prototype.updateChatMsgCache = function (msgCache, cb) {
    let collection = this.db.collection("common");
    collection.updateOne({id: CommonEnum.COMMON_DB_ID.CHAT_MSG_CACHE}, { $set:
            {msgCache: msgCache}},{upsert:true, w: 1}).then(function(result) {
        return cb(null)});
};


ClusterDao.prototype.findOrCreateAssociationChatMsgCache = function (cb) {
    let collection = this.db.collection("common");
    collection.findOne({
        id: CommonEnum.COMMON_DB_ID.ASSOCIATION_MSG_CACHE
    }, function(err, doc) {
        if(!!err){
            logger.debug('findOrCreateAssociationChatMsgCache: get msgCache fail, id:', CommonEnum.COMMON_DB_ID.ASSOCIATION_MSG_CACHE);
            return cb(err);
        }
        if(!!doc){
            return cb(null, doc);
        }else{
            collection.insertOne({
                id: CommonEnum.COMMON_DB_ID.ASSOCIATION_MSG_CACHE
            }, {w: 1}, function(err){
                if(!!err){
                    return cb(err);
                }
                cb(null);
            });
        }
    });
};

ClusterDao.prototype.findOrCreateAllServiceOpenGift = function (cb) {
    let collection = this.db.collection("common");
    collection.findOne({
        id: CommonEnum.COMMON_DB_ID.ALL_SERVICE_GIFT
    }, function(err, doc) {
        if(!!err){
            logger.debug('findOrCreateAllServiceOpenGift: get fail, id:', CommonEnum.COMMON_DB_ID.ALL_SERVICE_GIFT);
            return cb(err);
        }
        if(!!doc){
            return cb(null, doc);
        }else{
            collection.insertOne({
                id: CommonEnum.COMMON_DB_ID.ALL_SERVICE_GIFT
            }, {w: 1}, function(err){
                if(!!err){
                    return cb(err);
                }
                cb(null);
            });
        }
    });
};

ClusterDao.prototype.findOrCreateBeliefClearTime = function (cb) {
    let collection = this.db.collection("common");
    collection.findOne({
        id: CommonEnum.COMMON_DB_ID.BELIEF_CLEAR_TIME
    }, function(err, doc) {
        if(!!err){
            logger.debug('findOrCreateAllServiceOpenGift: get fail, id:', CommonEnum.COMMON_DB_ID.BELIEF_CLEAR_TIME);
            return cb(err);
        }
        if(!!doc){
            return cb(null, doc);
        }else{
            collection.insertOne({
                id: CommonEnum.COMMON_DB_ID.BELIEF_CLEAR_TIME
            }, {w: 1}, function(err){
                if(!!err){
                    return cb(err);
                }
                cb(null);
            });
        }
    });
};

ClusterDao.prototype.updateChMatchData = function (msg, cb) {
    let collection = this.db.collection("common");
    collection.findOne({
        id: CommonEnum.COMMON_DB_ID.CHMATCH_DATA
    }, function(err, doc) {
        if(!!err){
            logger.debug('findOrCreateAllServiceOpenGift: get fail, id:', CommonEnum.COMMON_DB_ID.CHMATCH_DATA);
            return cb(err);
        }
        if(!!doc){
            doc.id = CommonEnum.COMMON_DB_ID.CHMATCH_DATA;
            doc.recordList.push({week: msg.week, time: msg.time, enrollNum: msg.enrollNum, campaignNum: msg.campaignNum, campaignCash: msg.campaignCash, campaignList: msg.campaignList});
            collection.updateOne({
                id: CommonEnum.COMMON_DB_ID.CHMATCH_DATA
            },{
                $set: doc
            }, function (err) {
                if(!!err){
                    logger.error('updateChMatchData updateOne err', err);
                    return cb(err);
                }
                logger.debug('updateChMatchData update success.');
                return cb(null);
            });
        }else{
            doc = {};
            doc.id = CommonEnum.COMMON_DB_ID.CHMATCH_DATA;
            doc.recordList = [{week: msg.week, time: msg.time, enrollNum: msg.enrollNum, campaignNum: msg.campaignNum, campaignCash: msg.campaignCash, campaignList: msg.campaignList}];
            collection.insertOne(doc, {w: 1}, function(err){
                if(!!err){
                    return cb(err);
                }
                cb(null);
            });
        }
    });
};

ClusterDao.prototype.updatePkMatchData = function (msg, cb) {
    let collection = this.db.collection("common");
    collection.findOne({
        id: CommonEnum.COMMON_DB_ID.PKMATCH_DATA
    }, function(err, doc) {
        if(!!err){
            logger.debug('findOrCreateAllServiceOpenGift: get fail, id:', CommonEnum.COMMON_DB_ID.PKMATCH_DATA);
            return cb(err);
        }
        if(!!doc){
            doc.id = CommonEnum.COMMON_DB_ID.PKMATCH_DATA;
            doc.recordList.push({week: msg.week, time: msg.time, betList: msg.betList, betNum: msg.betNum, clubBetList: msg.clubBetList, total: msg.total, postList: msg.postList});
            collection.updateOne({
                id: CommonEnum.COMMON_DB_ID.PKMATCH_DATA
            },{
                $set: doc
            }, function (err) {
                if(!!err){
                    logger.error('updatePkMatchData updateOne err', err);
                    return cb(err);
                }
                logger.debug('updatePkMatchData update success.');
                return cb(null);
            });
        }else{
            doc = {};
            doc.id = CommonEnum.COMMON_DB_ID.PKMATCH_DATA;
            doc.recordList = [{week: msg.week, time: msg.time, betList: msg.betList, betNum: msg.betNum, clubBetList: msg.clubBetList, total: msg.total, postList: msg.postList}];
            collection.insertOne(doc, {w: 1}, function(err){
                if(!!err){
                    return cb(err);
                }
                cb(null);
            });
        }
    });
};
//得到当前拉霸触发保底总金额数量
ClusterDao.prototype.getSlotsSecurityMoney = function (msg, cb) {
    let collection = this.db.collection("common");
    collection.findOne({
        id: CommonEnum.COMMON_DB_ID.SLOTS_DATA
    }, function(err, doc) {
        if(!!err){
            logger.debug('getSlotsSecurityMoney: get fail, id:', CommonEnum.COMMON_DB_ID.SLOTS_DATA);
            return cb(err, 0);
        }
        if(!!doc){
            doc.id = CommonEnum.COMMON_DB_ID.SLOTS_DATA;
            let playerList = [];
            let isHave = false;
            let recordList = {};
            for(let i in doc.recordList)
            {
                if(doc.recordList[i].periods === msg.periods)
                {
                    playerList = doc.recordList[i].playerList;
                    recordList = doc.recordList[i];
                    isHave = true;
                    break;
                }
            }
            if(!isHave)
            {
                return cb(null, 0);
            }
            cb(null, recordList.securityMoney);
        }else{
            cb(null ,0);
        }
    });
};
//每期拉霸中总参与人数与活动金额
ClusterDao.prototype.updateSlotsData = function (msg, cb) {
    let collection = this.db.collection("common");
    collection.findOne({
        id: CommonEnum.COMMON_DB_ID.SLOTS_DATA
    }, function(err, doc) {
        if(!!err){
            logger.debug('findOrCreateAllServiceOpenGift: get fail, id:', CommonEnum.COMMON_DB_ID.SLOTS_DATA);
            return cb(err);
        }
        if(!!doc){
            doc.id = CommonEnum.COMMON_DB_ID.SLOTS_DATA;
            let playerList = [];
            let isHave = false;
            let recordList = {};
            for(let i in doc.recordList)
            {
                if(doc.recordList[i].periods === msg.periods)
                {
                    playerList = doc.recordList[i].playerList;
                    recordList = doc.recordList[i];
                    isHave = true;
                    break;
                }

            }
            let flag = false;
            for(let i in playerList) {
                if (playerList[i] === msg.playerId)
                {
                    flag = true;
                    break;
                }
            }
            if(!flag)
            {
                playerList.push(msg.playerId);
            }
            recordList.playerNum = playerList.length;
            recordList.totalMoney += msg.money;
            recordList.securityMoney = msg.securityMoney;
            if(!isHave)
            {
                doc.recordList.push({periods: msg.periods, playerNum: playerList.length, playerList: playerList, totalMoney: msg.money, securityMoney: msg.securityMoney});
            }
            collection.updateOne({
                id: CommonEnum.COMMON_DB_ID.SLOTS_DATA
            },{
                $set: doc
            }, function (err) {
                if(!!err){
                    logger.error('updateSlotsData updateOne err', err);
                    return cb(err);
                }
                logger.debug('updateSlotsData update success.');
                return cb(null);
            });
        }else{
            doc = {};
            doc.id = CommonEnum.COMMON_DB_ID.SLOTS_DATA;
            let playerList = [];
            playerList.push(msg.playerId);
            doc.recordList = [{periods: msg.periods, playerNum: playerList.length, playerList: playerList, totalMoney: msg.money, securityMoney: 0}];
            collection.insertOne(doc, {w: 1}, function(err){
                if(!!err){
                    return cb(err);
                }
                cb(null);
            });
        }
    });
};

//更新表
ClusterDao.prototype.updateConfigByTableName = function(tableName, obj, cb){
    let collection = this.db.collection("configData");
    collection.findOne({
        tableName: tableName
    }, function(err, doc) {
        if(!!err){
            logger.debug('updateConfigByTableName: fail', tableName);
            return cb(Code.FAIL);
        }

        if(!!doc){
            collection.updateOne({
                tableName: tableName
            },{
                $set: {data: obj}
            }, function (err) {
                if(!!err){
                    return cb(Code.FAIL);
                }
                logger.debug('updateConfigByTableName update success.');
                return cb(Code.OK);
            });
        }else{
            collection.insertOne({
                tableName: tableName,
                data: obj
            }, {w: 1}, function(err){
                if(!!err){
                    return cb(Code.FAIL);
                }
                return cb(Code.OK);
            });
        }
    });
};

//读所有配置表
ClusterDao.prototype.readWholeConfigData = function(callback){
    let collection = this.db.collection("configData");
    collection.find().toArray().then(allConfig => callback(allConfig));
};