/**
 * 领取活动奖励
 * 
 * 微服务: activity
 * 模块: event
 * Controller: event
 * Pattern: event.claimReward
 * 
 * 使用AST自动生成于 2025-07-26T12:41:29.259Z
 */

const BaseAction = require('../../../core/base-action');

class EventclaimRewardAction extends BaseAction {
  static metadata = {
    name: '领取活动奖励',
    description: '领取活动奖励',
    category: 'activity',
    serviceName: 'activity',
    module: 'event',
    actionName: 'event.claimReward',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "eventId": {
            "type": "number",
            "required": true,
            "description": "eventId参数"
      },
      "rewardId": {
            "type": "number",
            "required": true,
            "description": "rewardId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, eventId, rewardId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      eventId,
      rewardId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '领取活动奖励成功'
      };
    } else {
      throw new Error(`领取活动奖励失败: ${response.message}`);
    }
  }
}

module.exports = EventclaimRewardAction;