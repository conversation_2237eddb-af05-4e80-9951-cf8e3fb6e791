/**
 * 搜索公会
 * 
 * 微服务: social
 * 模块: guild
 * Controller: guild
 * Pattern: guild.search
 * 
 * 使用AST自动生成于 2025-07-26T12:41:29.597Z
 */

const BaseAction = require('../../../core/base-action');

class GuildsearchAction extends BaseAction {
  static metadata = {
    name: '搜索公会',
    description: '搜索公会',
    category: 'social',
    serviceName: 'social',
    module: 'guild',
    actionName: 'guild.search',
    prerequisites: ["login","character"],
    params: {
      "keyword": {
            "type": "string",
            "required": true,
            "description": "keyword参数"
      },
      "page": {
            "type": "number",
            "required": false,
            "description": "page参数"
      },
      "limit": {
            "type": "number",
            "required": false,
            "description": "limit参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { keyword, page, limit } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      keyword,
      page,
      limit
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '搜索公会成功'
      };
    } else {
      throw new Error(`搜索公会失败: ${response.message}`);
    }
  }
}

module.exports = GuildsearchAction;