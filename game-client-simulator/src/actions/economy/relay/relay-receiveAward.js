/**
 * 领取奖励 对应old项目中的receiveAward方法
 * 
 * 微服务: economy
 * 模块: relay
 * Controller: relay
 * Pattern: relay.receiveAward
 * 
 * 使用AST自动生成于 2025-07-26T12:41:29.397Z
 */

const BaseAction = require('../../../core/base-action');

class RelayreceiveAwardAction extends BaseAction {
  static metadata = {
    name: '领取奖励 对应old项目中的receiveAward方法',
    description: '领取奖励 对应old项目中的receiveAward方法',
    category: 'economy',
    serviceName: 'economy',
    module: 'relay',
    actionName: 'relay.receiveAward',
    prerequisites: ["login","character"],
    params: {
      "uid": {
            "type": "string",
            "required": true,
            "description": "uid参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "awardId": {
            "type": "number",
            "required": true,
            "description": "awardId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { uid, serverId, awardId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      uid,
      serverId,
      awardId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '领取奖励 对应old项目中的receiveAward方法成功'
      };
    } else {
      throw new Error(`领取奖励 对应old项目中的receiveAward方法失败: ${response.message}`);
    }
  }
}

module.exports = RelayreceiveAwardAction;