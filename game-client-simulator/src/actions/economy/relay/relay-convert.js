/**
 * 商城兑换 对应old项目中的convertibility方法
 * 
 * 微服务: economy
 * 模块: relay
 * Controller: relay
 * Pattern: relay.convert
 * 
 * 使用AST自动生成于 2025-07-26T12:41:29.396Z
 */

const BaseAction = require('../../../core/base-action');

class RelayconvertAction extends BaseAction {
  static metadata = {
    name: '商城兑换 对应old项目中的convertibility方法',
    description: '商城兑换 对应old项目中的convertibility方法',
    category: 'economy',
    serviceName: 'economy',
    module: 'relay',
    actionName: 'relay.convert',
    prerequisites: ["login","character"],
    params: {
      "uid": {
            "type": "string",
            "required": true,
            "description": "uid参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "id": {
            "type": "number",
            "required": true,
            "description": "id参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { uid, serverId, id } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      uid,
      serverId,
      id
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '商城兑换 对应old项目中的convertibility方法成功'
      };
    } else {
      throw new Error(`商城兑换 对应old项目中的convertibility方法失败: ${response.message}`);
    }
  }
}

module.exports = RelayconvertAction;