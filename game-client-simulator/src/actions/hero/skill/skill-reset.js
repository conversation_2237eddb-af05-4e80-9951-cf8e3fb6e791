/**
 * 重置球员技能
 * 
 * 微服务: hero
 * 模块: skill
 * Controller: skill
 * Pattern: skill.reset
 * 
 * 使用AST自动生成于 2025-07-26T12:41:29.501Z
 */

const BaseAction = require('../../../core/base-action');

class SkillresetAction extends BaseAction {
  static metadata = {
    name: '重置球员技能',
    description: '重置球员技能',
    category: 'hero',
    serviceName: 'hero',
    module: 'skill',
    actionName: 'skill.reset',
    prerequisites: ["login","character"],
    params: {
      "heroId": {
            "type": "string",
            "required": true,
            "description": "heroId参数"
      },
      "resetType": {
            "type": "object",
            "required": true,
            "description": "resetType参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { heroId, resetType, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      heroId,
      resetType,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '重置球员技能成功'
      };
    } else {
      throw new Error(`重置球员技能失败: ${response.message}`);
    }
  }
}

module.exports = SkillresetAction;