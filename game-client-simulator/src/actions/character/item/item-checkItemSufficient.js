/**
 * 检查物品数量是否足够 对应old项目: checkItemIsEnough方法，优化API命名
 * 
 * 微服务: character
 * 模块: item
 * Controller: item
 * Pattern: item.checkItemSufficient
 * 
 * 使用AST自动生成于 2025-07-26T12:41:29.360Z
 */

const BaseAction = require('../../../core/base-action');

class ItemcheckItemSufficientAction extends BaseAction {
  static metadata = {
    name: '检查物品数量是否足够 对应old项目: checkItemIsEnough方法，优化API命名',
    description: '检查物品数量是否足够 对应old项目: checkItemIsEnough方法，优化API命名',
    category: 'character',
    serviceName: 'character',
    module: 'item',
    actionName: 'item.checkItemSufficient',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "configId": {
            "type": "number",
            "required": true,
            "description": "configId参数"
      },
      "requiredQuantity": {
            "type": "number",
            "required": true,
            "description": "requiredQuantity参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, configId, requiredQuantity, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      configId,
      requiredQuantity,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '检查物品数量是否足够 对应old项目: checkItemIsEnough方法，优化API命名成功'
      };
    } else {
      throw new Error(`检查物品数量是否足够 对应old项目: checkItemIsEnough方法，优化API命名失败: ${response.message}`);
    }
  }
}

module.exports = ItemcheckItemSufficientAction;