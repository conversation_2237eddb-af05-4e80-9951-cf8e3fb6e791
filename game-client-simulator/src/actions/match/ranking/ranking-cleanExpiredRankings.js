/**
 * 清理过期排名数据（管理接口）
 * 
 * 微服务: match
 * 模块: ranking
 * Controller: ranking
 * Pattern: ranking.cleanExpiredRankings
 * 
 * 使用AST自动生成于 2025-07-26T12:41:29.543Z
 */

const BaseAction = require('../../../core/base-action');

class RankingcleanExpiredRankingsAction extends BaseAction {
  static metadata = {
    name: '清理过期排名数据（管理接口）',
    description: '清理过期排名数据（管理接口）',
    category: 'match',
    serviceName: 'match',
    module: 'ranking',
    actionName: 'ranking.cleanExpiredRankings',
    prerequisites: ["login","character"],
    params: {},
    timeout: 10000
  };

  async perform(client, params) {
    // 无参数
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '清理过期排名数据（管理接口）成功'
      };
    } else {
      throw new Error(`清理过期排名数据（管理接口）失败: ${response.message}`);
    }
  }
}

module.exports = RankingcleanExpiredRankingsAction;