/**
 * 获取世界杯信息 基于old项目的getWorldCupInfo接口
 * 
 * 微服务: match
 * 模块: tournament
 * Controller: tournament
 * Pattern: tournament.getWorldCupInfo
 * 
 * 使用AST自动生成于 2025-07-26T12:41:29.545Z
 */

const BaseAction = require('../../../core/base-action');

class TournamentgetWorldCupInfoAction extends BaseAction {
  static metadata = {
    name: '获取世界杯信息 基于old项目的getWorldCupInfo接口',
    description: '获取世界杯信息 基于old项目的getWorldCupInfo接口',
    category: 'match',
    serviceName: 'match',
    module: 'tournament',
    actionName: 'tournament.getWorldCupInfo',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取世界杯信息 基于old项目的getWorldCupInfo接口成功'
      };
    } else {
      throw new Error(`获取世界杯信息 基于old项目的getWorldCupInfo接口失败: ${response.message}`);
    }
  }
}

module.exports = TournamentgetWorldCupInfoAction;