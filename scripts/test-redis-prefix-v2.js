#!/usr/bin/env node

/**
 * Redis前缀架构v2.0测试脚本
 * 验证新的前缀架构是否正确实施
 */

const Redis = require('ioredis');
const { execSync } = require('child_process');

// 测试配置
const TEST_CONFIG = {
  redis: {
    host: process.env.REDIS_HOST || '***************',
    port: process.env.REDIS_PORT || 6379,
    password: process.env.REDIS_PASSWORD || '123456',
  },
  services: ['gateway', 'auth', 'user', 'game', 'club'],
  environment: process.env.NODE_ENV || 'dev',
  project: process.env.PROJECT_NAME || 'fm',
};

class RedisPrefixTester {
  constructor() {
    this.redis = new Redis(TEST_CONFIG.redis);
    this.testResults = [];
  }

  async runAllTests() {
    console.log('🚀 开始Redis前缀架构v2.0测试...\n');

    try {
      await this.testRedisConnection();
      await this.testServicePrefixes();
      await this.testDatabaseSeparation();
      await this.testKeyFormatValidation();
      await this.testBackupCompatibility();
      
      this.printResults();
    } catch (error) {
      console.error('❌ 测试过程中发生错误:', error);
    } finally {
      await this.cleanup();
    }
  }

  async testRedisConnection() {
    console.log('📡 测试Redis连接...');
    
    try {
      const pong = await this.redis.ping();
      this.addResult('Redis连接', pong === 'PONG', '连接成功');
    } catch (error) {
      this.addResult('Redis连接', false, `连接失败: ${error.message}`);
    }
  }

  async testServicePrefixes() {
    console.log('🏷️  测试服务前缀...');

    for (const service of TEST_CONFIG.services) {
      const prefix = `${TEST_CONFIG.environment}:${TEST_CONFIG.project}:${service}:`;
      const testKey = `${prefix}test:${Date.now()}`;
      const testValue = { service, timestamp: Date.now() };

      try {
        // 设置测试键
        await this.redis.set(testKey, JSON.stringify(testValue));
        
        // 验证键存在
        const exists = await this.redis.exists(testKey);
        
        // 验证键格式
        const keys = await this.redis.keys(`${prefix}*`);
        const hasCorrectFormat = keys.some(key => key === testKey);

        this.addResult(
          `${service}服务前缀`,
          exists && hasCorrectFormat,
          `前缀: ${prefix}, 键数: ${keys.length}`
        );

        // 清理测试键
        await this.redis.del(testKey);
      } catch (error) {
        this.addResult(`${service}服务前缀`, false, `错误: ${error.message}`);
      }
    }
  }

  async testDatabaseSeparation() {
    console.log('🗄️  测试数据库分离...');

    const databaseMapping = {
      'gateway': 0,
      'auth': 1,
      'user': 2,
      'game': 3,
      'club': 4,
    };

    for (const [service, dbNumber] of Object.entries(databaseMapping)) {
      try {
        const dbClient = this.redis.duplicate();
        await dbClient.select(dbNumber);

        const testKey = `test:${service}:${Date.now()}`;
        const testValue = `test-value-${service}`;

        // 在指定数据库中设置键
        await dbClient.set(testKey, testValue);

        // 验证键存在
        const value = await dbClient.get(testKey);
        const exists = value === testValue;

        // 验证键不在其他数据库中
        const otherDbClient = this.redis.duplicate();
        await otherDbClient.select(dbNumber === 0 ? 1 : 0);
        const notInOtherDb = !(await otherDbClient.exists(testKey));

        this.addResult(
          `${service}数据库分离(DB${dbNumber})`,
          exists && notInOtherDb,
          `键存在: ${exists}, 隔离: ${notInOtherDb}`
        );

        // 清理
        await dbClient.del(testKey);
        await dbClient.quit();
        await otherDbClient.quit();
      } catch (error) {
        this.addResult(`${service}数据库分离`, false, `错误: ${error.message}`);
      }
    }
  }

  async testKeyFormatValidation() {
    console.log('🔍 测试键格式验证...');

    const testCases = [
      {
        name: '正确格式',
        key: 'dev:fm:auth:user:profile:123',
        expected: true,
      },
      {
        name: '缺少环境',
        key: 'fm:auth:user:profile:123',
        expected: false,
      },
      {
        name: '缺少项目',
        key: 'dev:auth:user:profile:123',
        expected: false,
      },
      {
        name: '缺少服务',
        key: 'dev:fm:user:profile:123',
        expected: false,
      },
    ];

    for (const testCase of testCases) {
      const isValid = this.validateKeyFormat(testCase.key);
      this.addResult(
        `键格式-${testCase.name}`,
        isValid === testCase.expected,
        `键: ${testCase.key}, 验证: ${isValid}`
      );
    }
  }

  async testBackupCompatibility() {
    console.log('💾 测试备份兼容性...');

    try {
      // 创建测试数据
      const testData = {};
      for (const service of TEST_CONFIG.services) {
        const prefix = `${TEST_CONFIG.environment}:${TEST_CONFIG.project}:${service}:`;
        const key = `${prefix}backup:test:${Date.now()}`;
        const value = { service, test: true, timestamp: Date.now() };
        
        await this.redis.set(key, JSON.stringify(value));
        testData[service] = { key, value };
      }

      // 测试服务感知的键匹配
      for (const service of TEST_CONFIG.services) {
        const pattern = `${TEST_CONFIG.environment}:${TEST_CONFIG.project}:${service}:*`;
        const keys = await this.redis.keys(pattern);
        const hasTestKey = keys.some(key => key.includes('backup:test'));

        this.addResult(
          `备份-${service}服务`,
          hasTestKey,
          `模式: ${pattern}, 匹配键数: ${keys.length}`
        );
      }

      // 清理测试数据
      for (const { key } of Object.values(testData)) {
        await this.redis.del(key);
      }
    } catch (error) {
      this.addResult('备份兼容性', false, `错误: ${error.message}`);
    }
  }

  validateKeyFormat(key) {
    const parts = key.split(':');
    if (parts.length < 4) return false;

    const [env, project, service] = parts;
    
    // 验证环境
    if (!['dev', 'test', 'prod'].includes(env)) return false;
    
    // 验证项目
    if (project !== 'fm') return false;
    
    // 验证服务
    if (!TEST_CONFIG.services.includes(service)) return false;

    return true;
  }

  addResult(test, passed, details) {
    this.testResults.push({
      test,
      passed,
      details,
      timestamp: new Date().toISOString(),
    });

    const status = passed ? '✅' : '❌';
    console.log(`  ${status} ${test}: ${details}`);
  }

  printResults() {
    console.log('\n📊 测试结果汇总:');
    console.log('='.repeat(60));

    const passed = this.testResults.filter(r => r.passed).length;
    const total = this.testResults.length;
    const passRate = ((passed / total) * 100).toFixed(1);

    console.log(`总测试数: ${total}`);
    console.log(`通过数: ${passed}`);
    console.log(`失败数: ${total - passed}`);
    console.log(`通过率: ${passRate}%`);

    if (passed === total) {
      console.log('\n🎉 所有测试通过！Redis前缀架构v2.0实施成功！');
    } else {
      console.log('\n⚠️  部分测试失败，请检查配置和实施。');
      
      console.log('\n失败的测试:');
      this.testResults
        .filter(r => !r.passed)
        .forEach(r => {
          console.log(`  ❌ ${r.test}: ${r.details}`);
        });
    }

    // 保存测试报告
    this.saveTestReport();
  }

  saveTestReport() {
    const report = {
      timestamp: new Date().toISOString(),
      config: TEST_CONFIG,
      results: this.testResults,
      summary: {
        total: this.testResults.length,
        passed: this.testResults.filter(r => r.passed).length,
        failed: this.testResults.filter(r => !r.passed).length,
      },
    };

    const fs = require('fs');
    const path = require('path');
    
    const reportDir = path.join(__dirname, '..', 'test-reports');
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }

    const reportFile = path.join(reportDir, `redis-prefix-v2-test-${Date.now()}.json`);
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
    
    console.log(`\n📄 测试报告已保存: ${reportFile}`);
  }

  async cleanup() {
    console.log('\n🧹 清理测试环境...');
    await this.redis.quit();
  }
}

// 运行测试
if (require.main === module) {
  const tester = new RedisPrefixTester();
  tester.runAllTests().catch(console.error);
}

module.exports = RedisPrefixTester;
