#!/usr/bin/env node

/**
 * 测试 verifyToken API 的脚本
 * 验证 HTTP 版本的令牌验证功能
 */

const axios = require('axios');
const chalk = require('chalk');

// 配置
const CONFIG = {
  AUTH_URL: 'http://127.0.0.1:3001',
  GATEWAY_URL: 'http://127.0.0.1:3000',
};

class VerifyTokenTester {
  constructor() {
    this.results = [];
    this.validToken = null;
    this.expiredToken = null;
  }

  /**
   * 运行所有测试
   */
  async runTests() {
    console.log(chalk.blue('🔐 开始测试 verifyToken API...\n'));

    try {
      // 1. 获取有效令牌
      await this.getValidToken();

      // 2. 测试直接访问认证服务
      await this.testDirectAuthService();

      // 3. 测试通过网关代理
      await this.testGatewayProxy();

      // 4. 测试无效令牌
      await this.testInvalidTokens();

      // 5. 测试新的用户信息 API
      await this.testUserInfoAPI();

      // 输出结果
      this.printResults();

    } catch (error) {
      console.error(chalk.red('❌ 测试执行失败:'), error.message);
      process.exit(1);
    }
  }

  /**
   * 获取有效令牌
   */
  async getValidToken() {
    console.log(chalk.yellow('🔑 获取有效令牌...'));

    try {
      // 创建测试用户
      const userData = {
        username: `tokentest_${Date.now()}`,
        email: `tokentest_${Date.now()}@example.com`,
        password: 'SecureP@ssw0rd!',
        confirmPassword: 'SecureP@ssw0rd!',
        acceptTerms: true,
        profile: {
          firstName: 'Token',
          lastName: 'Test'
        }
      };

      await axios.post(`${CONFIG.AUTH_URL}/auth/register`, userData);

      // 登录获取令牌
      const loginResponse = await axios.post(`${CONFIG.AUTH_URL}/auth/login`, {
        identifier: userData.username,
        password: userData.password
      });

      this.validToken = loginResponse.data.data.tokens.accessToken;
      this.addResult('获取有效令牌', true, '令牌获取成功');

    } catch (error) {
      this.addResult('获取有效令牌', false, error.message);
      throw error;
    }
  }

  /**
   * 测试直接访问认证服务
   */
  async testDirectAuthService() {
    console.log(chalk.yellow('🎯 测试直接访问认证服务...'));

    try {
      // 测试有效令牌
      const response = await axios.post(`${CONFIG.AUTH_URL}/auth/verify-token`, {
        token: this.validToken
      });

      const isValid = response.data.success && response.data.data.valid;
      const hasUserInfo = response.data.data.user && response.data.data.user.id;

      this.addResult('直接验证有效令牌', isValid, 
        `令牌状态: ${response.data.data.valid}, 用户ID: ${response.data.data.user?.id}`);
      
      this.addResult('返回用户信息', hasUserInfo, 
        `用户名: ${response.data.data.user?.username}`);

    } catch (error) {
      this.addResult('直接访问认证服务', false, error.message);
    }
  }

  /**
   * 测试通过网关代理
   */
  async testGatewayProxy() {
    console.log(chalk.yellow('🌐 测试通过网关代理...'));

    try {
      // 测试网关代理到认证服务
      const response = await axios.post(`${CONFIG.GATEWAY_URL}/api/auth/auth/verify-token`, {
        token: this.validToken
      });

      const isValid = response.data.success && response.data.data.valid;
      this.addResult('网关代理验证令牌', isValid, 
        `通过网关验证令牌: ${response.data.data.valid}`);

    } catch (error) {
      this.addResult('网关代理验证', false, error.message);
    }
  }

  /**
   * 测试无效令牌
   */
  async testInvalidTokens() {
    console.log(chalk.yellow('❌ 测试无效令牌...'));

    const invalidTokens = [
      { name: '空令牌', token: '' },
      { name: '格式错误令牌', token: 'invalid-token' },
      { name: '过期令牌', token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.invalid' },
    ];

    for (const testCase of invalidTokens) {
      try {
        const response = await axios.post(`${CONFIG.AUTH_URL}/auth/verify-token`, {
          token: testCase.token
        });

        const isInvalid = !response.data.data.valid;
        this.addResult(`无效令牌测试 - ${testCase.name}`, isInvalid, 
          `正确识别为无效: ${!response.data.data.valid}`);

      } catch (error) {
        // 对于格式错误的令牌，可能会抛出异常，这也是正确的行为
        this.addResult(`无效令牌测试 - ${testCase.name}`, true, 
          `正确抛出异常: ${error.response?.status || error.message}`);
      }
    }
  }

  /**
   * 测试新的用户信息 API
   */
  async testUserInfoAPI() {
    console.log(chalk.yellow('👤 测试用户信息 API...'));

    try {
      // 测试需要认证的用户信息 API
      const response = await axios.post(`${CONFIG.AUTH_URL}/auth/user-info`, {}, {
        headers: {
          'Authorization': `Bearer ${this.validToken}`
        }
      });

      const hasUserInfo = response.data.success && response.data.data.user;
      this.addResult('用户信息 API', hasUserInfo, 
        `获取用户信息: ${response.data.data.user?.username}`);

    } catch (error) {
      this.addResult('用户信息 API', false, error.message);
    }

    try {
      // 测试无认证访问用户信息 API（应该失败）
      await axios.post(`${CONFIG.AUTH_URL}/auth/user-info`, {});
      this.addResult('用户信息 API 安全性', false, '未认证请求应该被拒绝');

    } catch (error) {
      const isUnauthorized = error.response?.status === 401;
      this.addResult('用户信息 API 安全性', isUnauthorized, 
        `正确拒绝未认证请求: ${error.response?.status}`);
    }
  }

  /**
   * 添加测试结果
   */
  addResult(testName, success, details) {
    this.results.push({ testName, success, details });
    
    const status = success ? chalk.green('✅') : chalk.red('❌');
    const message = success ? chalk.green('成功') : chalk.red('失败');
    
    console.log(`  ${status} ${testName}: ${message}`);
    if (details) {
      console.log(`     详情: ${details}`);
    }
  }

  /**
   * 打印测试结果
   */
  printResults() {
    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;

    console.log('\n' + '='.repeat(60));
    console.log(chalk.blue('📊 verifyToken API 测试结果'));
    console.log('='.repeat(60));
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${chalk.green(passedTests)}`);
    console.log(`失败: ${chalk.red(failedTests)}`);
    console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    if (failedTests > 0) {
      console.log(chalk.red('\n失败的测试:'));
      this.results
        .filter(r => !r.success)
        .forEach(r => {
          console.log(`  ❌ ${r.testName}: ${r.details}`);
        });
    }

    console.log(chalk.blue('\n测试完成!'));

    if (passedTests === totalTests) {
      console.log(chalk.green('🎉 所有测试通过！verifyToken API 工作正常。'));
    }
  }
}

// 运行测试
const tester = new VerifyTokenTester();
tester.runTests().catch(error => {
  console.error(chalk.red('❌ 测试失败:'), error);
  process.exit(1);
});
