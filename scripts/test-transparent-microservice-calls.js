/**
 * 透明微服务调用测试脚本 (microservice-kit 版本)
 *
 * 测试目标：
 * 1. 新的 microservice-kit 库微服务调用验证
 * 2. ConfigModule 配置的微服务通信
 * 3. 透明微服务调用机制 (通过WebSocket)
 * 4. HTTP代理与WebSocket调用的协同工作
 * 5. 微服务方法注册和发现
 * 6. 错误处理和重试机制
 * 7. Redis 传输层验证 (内网服务器)
 * 8. 性能和稳定性测试
 */

const { io } = require('socket.io-client');
const axios = require('axios');
const chalk = require('chalk');

// 配置
const CONFIG = {
  GATEWAY_URL: 'http://127.0.0.1:3000',
  GATEWAY_WS_URL: 'http://127.0.0.1:3000',
  TEST_TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3,
  MICROSERVICES: {
    AUTH_SERVICE: 'AUTH_SERVICE',
    CHARACTER_SERVICE: 'CHARACTER_SERVICE',
    GAME_SERVICE: 'GAME_SERVICE'
  },
  REDIS_CONFIG: {
    host: '***************',
    port: 6379,
    password: '123456',
    db: 6  // 微服务通信专用数据库
  }
};

class TransparentCallTester {
  constructor() {
    this.socket = null;
    this.testResults = [];
    this.pendingRequests = new Map();
  }

  /**
   * 运行透明调用测试
   */
  async runTests() {
    console.log(chalk.blue('🚀 开始透明微服务调用测试 (microservice-kit 集成)\n'));

    try {
      // 1. 验证 microservice-kit 配置
      await this.testMicroserviceKitConfiguration();

      // 2. 测试新路由系统基础功能
      await this.testNewRoutingSystemIntegration();

      // 3. 建立WebSocket连接
      await this.connectWebSocket();

      // 4. 测试方法注册验证
      await this.testMethodRegistration();

      // 5. 测试基本透明调用
      await this.testBasicTransparentCall();

      // 6. 测试带参数的调用
      await this.testParameterizedCall();

      // 7. 测试HTTP代理与WebSocket调用协同
      await this.testHttpWebSocketIntegration();

      // 8. 测试错误处理
      await this.testErrorHandling();

      // 9. 测试批量调用
      await this.testBatchCalls();

      // 10. 测试性能和稳定性
      await this.testPerformanceAndStability();

      // 输出结果
      this.printResults();

    } catch (error) {
      console.error(chalk.red('❌ 测试执行失败:'), error.message);
    } finally {
      if (this.socket) {
        this.socket.disconnect();
      }
    }
  }

  /**
   * 验证 microservice-kit 配置
   */
  async testMicroserviceKitConfiguration() {
    console.log(chalk.yellow('⚙️ 验证 microservice-kit 配置...'));

    const axios = require('axios');

    try {
      // 测试网关服务的 microservice-kit 配置
      const gatewayHealth = await axios.get(`http://127.0.0.1:3000/health/detailed`, { timeout: 5000 });
      const gatewayRedisStatus = gatewayHealth.data?.details?.redis?.status;
      this.addResult('网关 Redis 连接', gatewayRedisStatus === 'up', `网关 Redis 状态: ${gatewayRedisStatus}`);

      // 测试认证服务的 microservice-kit 配置
      const authHealth = await axios.get(`http://127.0.0.1:3001/health/detailed`, { timeout: 5000 });
      const authRedisStatus = authHealth.data?.details?.redis?.status;
      this.addResult('认证服务 Redis 连接', authRedisStatus === 'up', `认证服务 Redis 状态: ${authRedisStatus}`);

      // 验证 Redis 配置是否使用内网地址
      const expectedHost = CONFIG.REDIS_CONFIG.host;
      this.addResult('Redis 配置验证', expectedHost === '***************', `使用内网 Redis: ${expectedHost}`);

      console.log(chalk.green('✅ microservice-kit 配置验证完成'));

    } catch (error) {
      this.addResult('microservice-kit 配置验证', false, error.message);
      console.log(chalk.red('❌ microservice-kit 配置验证失败:', error.message));
    }
  }

  /**
   * 测试新路由系统基础功能
   */
  async testNewRoutingSystemIntegration() {
    console.log(chalk.yellow('🛣️ 测试新路由系统集成...'));

    const axios = require('axios');

    try {
      // 测试系统路由
      const healthResponse = await axios.get(`http://127.0.0.1:3000/health`, { timeout: 5000 });
      this.addResult('系统路由 - /health', healthResponse.status === 200, '网关健康检查');

      const docsResponse = await axios.get(`http://127.0.0.1:3000/docs`, { timeout: 5000 });
      this.addResult('系统路由 - /docs', docsResponse.status === 200, 'Swagger文档');

      // 测试微服务路由
      const authHealthResponse = await axios.get(`http://127.0.0.1:3000/api/auth/health`, { timeout: 5000 });
      this.addResult('微服务路由 - /api/auth/health', authHealthResponse.status === 200, '认证服务健康检查');

      // 测试路径重写验证
      const isFromAuthService = authHealthResponse.data &&
        (authHealthResponse.data.service === 'auth' || authHealthResponse.data.status === 'ok');
      this.addResult('路径重写验证', isFromAuthService, '请求正确代理到认证服务');

      // 测试错误处理
      try {
        await axios.get(`http://127.0.0.1:3000/api/unknown/test`, { timeout: 5000 });
        this.addResult('错误处理 - 未知服务', false, '应该返回404');
      } catch (error) {
        this.addResult('错误处理 - 未知服务', error.response?.status === 404, '正确返回404');
      }

    } catch (error) {
      this.addResult('新路由系统集成测试', false, error.message);
    }
  }

  /**
   * 获取认证令牌
   */
  async getAuthToken() {
    if (this.authToken) return this.authToken;

    try {
      // 创建测试用户
      const userData = {
        username: `mstest_${Date.now()}`,
        email: `mstest_${Date.now()}@example.com`,
        password: 'SecureP@ssw0rd!',
        confirmPassword: 'SecureP@ssw0rd!',
        acceptTerms: true,
        profile: {
          firstName: 'Microservice',
          lastName: 'Test'
        }
      };

      await axios.post(`${CONFIG.GATEWAY_URL}/api/auth/auth/register`, userData);

      // 登录获取令牌
      const loginResponse = await axios.post(`${CONFIG.GATEWAY_URL}/api/auth/auth/login`, {
        identifier: userData.username,
        password: userData.password
      });

      this.authToken = loginResponse.data.data.tokens.accessToken;
      this.addResult('获取认证令牌', true, '令牌获取成功');
      return this.authToken;
    } catch (error) {
      this.addResult('获取认证令牌', false, error.message);
      throw new Error(`获取认证令牌失败: ${error.message}`);
    }
  }

  /**
   * 建立Socket.IO连接
   */
  async connectWebSocket() {
    console.log(chalk.yellow('🔌 建立WebSocket连接...'));

    // 先获取认证令牌
    const token = await this.getAuthToken();

    return new Promise((resolve, reject) => {
      this.socket = io(CONFIG.GATEWAY_WS_URL, {
        auth: { token },  // 传递认证令牌
        transports: ['websocket', 'polling'],
        timeout: CONFIG.TEST_TIMEOUT,
        forceNew: true
      });

      this.socket.on('connect', () => {
        this.addResult('WebSocket连接', true, '连接成功');

        // 设置消息响应处理器 - 网关通过 'message' 事件返回响应
        this.socket.on('message', (message) => {
          this.handleWebSocketMessage(message);
        });

        resolve();
      });

      this.socket.on('connect_error', (error) => {
        this.addResult('WebSocket连接', false, error.message);
        reject(error);
      });

      setTimeout(() => {
        if (!this.socket.connected) {
          this.addResult('WebSocket连接', false, '连接超时');
          reject(new Error('WebSocket连接超时'));
        }
      }, 5000);
    });
  }

  /**
   * 处理WebSocket消息响应
   */
  handleWebSocketMessage(message) {
    try {
      // 网关返回的消息格式：{ id, type, service, action, payload, timestamp }
      if (message.id && this.pendingRequests.has(message.id)) {
        const { resolve } = this.pendingRequests.get(message.id);
        this.pendingRequests.delete(message.id);

        // 根据消息类型处理响应
        if (message.type === 'response') {
          resolve({
            success: message.payload.success,
            data: message.payload.data,
            error: message.payload.error
          });
        } else if (message.type === 'error') {
          resolve({
            success: false,
            error: message.payload.error
          });
        }
      }
    } catch (error) {
      console.error('响应处理失败:', error);
    }
  }

  /**
   * 发送微服务调用请求
   */
  async sendMicroserviceCall(service, method, params = {}, options = {}) {
    const requestId = `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 使用新的 command 格式
    const message = {
      id: requestId,
      command: `${service}.${method}`,  // 新格式：service.action
      payload: params
    };

    return new Promise((resolve, reject) => {
      // 注册请求
      this.pendingRequests.set(requestId, { resolve, reject });

      // 使用正确的消息事件 - 网关监听的是 'message'
      this.socket.emit('message', message);

      // 设置超时
      setTimeout(() => {
        if (this.pendingRequests.has(requestId)) {
          this.pendingRequests.delete(requestId);
          reject(new Error('请求超时'));
        }
      }, CONFIG.TEST_TIMEOUT);
    });
  }

  /**
   * 测试方法注册验证
   */
  async testMethodRegistration() {
    console.log(chalk.yellow('📋 测试方法注册验证...'));

    try {
      // 使用真实的认证令牌测试已注册的方法
      const response = await this.sendMicroserviceCall('auth', 'verifyToken', {
        token: this.authToken
      });

      // 如果方法未注册，应该返回 "Method not found" 错误
      // 如果方法已注册，应该返回具体的验证结果
      const isRegistered = response.success !== undefined; // 有响应就说明方法已注册

      this.addResult('方法注册验证 - auth.verifyToken', isRegistered, {
        isRegistered,
        tokenValid: response.success,
        response: response
      });

    } catch (error) {
      this.addResult('方法注册验证', false, error.message);
    }
  }

  /**
   * 测试基本透明调用
   */
  async testBasicTransparentCall() {
    console.log(chalk.yellow('🔄 测试基本透明调用...'));

    try {
      const response = await this.sendMicroserviceCall('auth', 'verifyToken', {
        token: this.authToken
      });

      // 验证响应结构
      const hasCorrectStructure = response.hasOwnProperty('success') && 
                                 response.hasOwnProperty('requestId');

      this.addResult('基本透明调用', hasCorrectStructure, {
        responseStructure: Object.keys(response),
        success: response.success,
        hasData: !!response.data,
        hasError: !!response.error
      });

    } catch (error) {
      this.addResult('基本透明调用', false, error.message);
    }
  }

  /**
   * 测试带参数的调用
   */
  async testParameterizedCall() {
    console.log(chalk.yellow('🎯 测试带参数的调用...'));

    try {
      const response = await this.sendMicroserviceCall('auth', 'checkPermission', {
        userId: 'test-user-123',
        resource: 'player',
        action: 'transfer'
      });

      const isValidResponse = response.success !== undefined;
      
      this.addResult('带参数的调用 - checkPermission', isValidResponse, {
        success: response.success,
        data: response.data,
        error: response.error
      });

    } catch (error) {
      this.addResult('带参数的调用', false, error.message);
    }
  }

  /**
   * 测试HTTP代理与WebSocket调用协同
   */
  async testHttpWebSocketIntegration() {
    console.log(chalk.yellow('🔄 测试HTTP代理与WebSocket调用协同...'));

    const axios = require('axios');

    try {
      // 1. 通过HTTP代理调用认证服务
      const httpResponse = await axios.get(`http://127.0.0.1:3000/api/auth/health`, { timeout: 5000 });
      const httpSuccess = httpResponse.status === 200;

      // 2. 通过WebSocket透明调用认证服务
      const wsResponse = await this.sendMicroserviceCall('auth', 'verifyToken', {
        token: this.authToken
      });
      const wsSuccess = wsResponse.success !== undefined;

      // 3. 验证两种方式都能正常工作
      this.addResult('HTTP代理调用', httpSuccess, {
        status: httpResponse.status,
        hasData: !!httpResponse.data
      });

      this.addResult('WebSocket透明调用', wsSuccess, {
        hasResponse: !!wsResponse,
        hasSuccess: wsResponse.success !== undefined,
        hasData: !!wsResponse.data || !!wsResponse.error
      });

      // 4. 验证协同工作
      this.addResult('HTTP与WebSocket协同工作', httpSuccess && wsSuccess,
        '两种调用方式都正常工作');

    } catch (error) {
      this.addResult('HTTP与WebSocket集成测试', false, error.message);
    }
  }

  /**
   * 测试错误处理
   */
  async testErrorHandling() {
    console.log(chalk.yellow('⚠️ 测试错误处理...'));

    try {
      // 1. 测试WebSocket微服务调用错误
      const response = await this.sendMicroserviceCall('auth', 'nonExistentMethod', {});

      const hasError = !response.success && response.error;
      const errorMessage = response.error || '';

      this.addResult('WebSocket错误 - 不存在的方法', hasError, {
        errorReturned: hasError,
        errorMessage: errorMessage,
        containsNotFound: errorMessage.includes('not found') || errorMessage.includes('Method')
      });

      // 2. 测试HTTP路由错误处理
      const axios = require('axios');

      try {
        await axios.get(`http://127.0.0.1:3000/api/user/health`, { timeout: 5000 });
        this.addResult('HTTP错误 - 服务不可用', false, '应该返回502');
      } catch (error) {
        this.addResult('HTTP错误 - 服务不可用',
          error.response?.status === 502,
          `返回状态: ${error.response?.status}`);
      }

    } catch (error) {
      this.addResult('错误处理', false, error.message);
    }
  }

  /**
   * 测试批量调用
   */
  async testBatchCalls() {
    console.log(chalk.yellow('📦 测试批量调用...'));

    try {
      const batchMessage = {
        event: 'microservice.batch',
        data: {
          calls: [
            {
              service: 'auth',
              method: 'verifyToken',
              params: { token: 'test-token-1' },
              requestId: 'batch_1'
            },
            {
              service: 'auth',
              method: 'checkPermission',
              params: { userId: 'test', resource: 'test', action: 'read' },
              requestId: 'batch_2'
            }
          ],
          requestId: 'test_batch_calls'
        }
      };

      const response = await new Promise((resolve, reject) => {
        const requestId = 'test_batch_calls';
        this.pendingRequests.set(requestId, { resolve, reject });
        
        this.socket.emit('message', batchMessage);
        
        setTimeout(() => {
          if (this.pendingRequests.has(requestId)) {
            this.pendingRequests.delete(requestId);
            reject(new Error('批量调用超时'));
          }
        }, CONFIG.TEST_TIMEOUT);
      });

      const isBatchResponse = Array.isArray(response.data);
      
      this.addResult('批量调用', isBatchResponse, {
        isArray: isBatchResponse,
        resultCount: response.data?.length || 0,
        results: response.data
      });

    } catch (error) {
      this.addResult('批量调用', false, error.message);
    }
  }

  /**
   * 测试缓存功能
   */
  async testCacheFeature() {
    console.log(chalk.yellow('💾 测试缓存功能...'));

    try {
      const testParams = { userId: 'cache-test-user' };
      
      // 第一次调用
      const startTime1 = Date.now();
      const response1 = await this.sendMicroserviceCall('auth', 'getUserInfo', testParams);
      const duration1 = Date.now() - startTime1;

      // 立即第二次调用（应该使用缓存）
      const startTime2 = Date.now();
      const response2 = await this.sendMicroserviceCall('auth', 'getUserInfo', testParams);
      const duration2 = Date.now() - startTime2;

      // 缓存命中的调用应该更快
      const possibleCacheHit = duration2 < duration1 * 0.8; // 第二次调用应该快至少20%

      this.addResult('缓存功能测试', true, {
        firstCallDuration: duration1,
        secondCallDuration: duration2,
        possibleCacheHit: possibleCacheHit,
        speedImprovement: duration1 > 0 ? ((duration1 - duration2) / duration1 * 100).toFixed(1) + '%' : 'N/A'
      });

    } catch (error) {
      this.addResult('缓存功能测试', false, error.message);
    }
  }

  /**
   * 测试性能和稳定性
   */
  async testPerformanceAndStability() {
    console.log(chalk.yellow('🚀 测试性能和稳定性...'));

    try {
      // 性能测试：批量调用
      const startTime = Date.now();
      const promises = [];
      const callCount = 10;

      for (let i = 0; i < callCount; i++) {
        promises.push(this.makeTransparentCall('auth.health', {}));
      }

      const results = await Promise.allSettled(promises);
      const endTime = Date.now();
      const duration = endTime - startTime;

      const successCount = results.filter(r => r.status === 'fulfilled').length;
      const avgTime = duration / callCount;

      this.addResult('批量调用性能', successCount === callCount,
        `${successCount}/${callCount} 成功, 平均耗时: ${avgTime.toFixed(2)}ms`);

      // 稳定性测试：连续调用
      let consecutiveSuccess = 0;
      for (let i = 0; i < 5; i++) {
        try {
          await this.makeTransparentCall('auth.health', {});
          consecutiveSuccess++;
          await new Promise(resolve => setTimeout(resolve, 100)); // 间隔100ms
        } catch (error) {
          break;
        }
      }

      this.addResult('连续调用稳定性', consecutiveSuccess === 5,
        `连续成功调用: ${consecutiveSuccess}/5`);

      // 错误恢复测试
      try {
        await this.makeTransparentCall('nonexistent.method', {});
        this.addResult('错误处理', false, '应该抛出错误');
      } catch (error) {
        this.addResult('错误处理', true, '正确处理不存在的方法调用');
      }

      console.log(chalk.green('✅ 性能和稳定性测试完成'));

    } catch (error) {
      this.addResult('性能和稳定性测试', false, error.message);
      console.log(chalk.red('❌ 性能和稳定性测试失败:', error.message));
    }
  }

  /**
   * 添加测试结果
   */
  addResult(testName, success, details) {
    this.testResults.push({
      name: testName,
      success,
      details,
      timestamp: new Date()
    });

    const status = success ? chalk.green('✅') : chalk.red('❌');
    console.log(`  ${status} ${testName}`);
    
    if (details && typeof details === 'object') {
      console.log(`     ${JSON.stringify(details, null, 2)}`);
    } else if (details) {
      console.log(`     ${details}`);
    }
  }

  /**
   * 打印测试结果
   */
  printResults() {
    console.log('\n' + chalk.blue('📊 透明微服务调用测试结果'));
    console.log('='.repeat(60));

    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;

    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${chalk.green(passedTests)}`);
    console.log(`失败: ${chalk.red(failedTests)}`);
    console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    if (failedTests > 0) {
      console.log('\n' + chalk.red('失败的测试:'));
      this.testResults
        .filter(r => !r.success)
        .forEach(r => {
          console.log(`  ❌ ${r.name}: ${typeof r.details === 'string' ? r.details : JSON.stringify(r.details)}`);
        });
    }

    console.log('\n' + chalk.blue('🎉 透明微服务调用测试完成!'));
  }
}

// 运行测试
if (require.main === module) {
  const tester = new TransparentCallTester();
  tester.runTests().catch(console.error);
}

module.exports = TransparentCallTester;
