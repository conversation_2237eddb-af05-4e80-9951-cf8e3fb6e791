#!/usr/bin/env node

/**
 * WebSocket代理缓存测试脚本
 * 验证通过WebSocket代理调用的缓存装饰器是否会正常工作
 */

const io = require('socket.io-client');
const axios = require('axios');
const chalk = require('chalk');

class WebSocketCacheTest {
  constructor() {
    this.socket = null;
    this.authToken = null;
    this.testResults = [];
    this.baseUrl = 'http://127.0.0.1:3001';
    this.gatewayUrl = 'http://127.0.0.1:3000';
    this.cacheHitCount = 0;
    this.cacheMissCount = 0;
  }

  /**
   * 设置认证
   */
  async setupAuthentication() {
    console.log(chalk.yellow('🔑 设置认证令牌...'));

    const userData = {
      username: `cachetest_${Date.now()}`,
      email: `cachetest_${Date.now()}@example.com`,
      password: 'SecureP@ssw0rd!',
      confirmPassword: 'SecureP@ssw0rd!',
      acceptTerms: true,
      profile: {
        firstName: 'Cache',
        lastName: 'Test'
      }
    };

    try {
      await axios.post('http://127.0.0.1:3001/auth/register', userData);
    } catch (error) {
      // 用户可能已存在，忽略错误
    }

    const loginResponse = await axios.post('http://127.0.0.1:3001/auth/login', {
      identifier: userData.username,
      password: userData.password
    });

    this.authToken = loginResponse.data.data.tokens.accessToken;
    console.log(chalk.green('✅ 认证令牌获取成功'));
    return this.authToken;
  }

  /**
   * 连接WebSocket
   */
  async connectWebSocket(token) {
    console.log(chalk.yellow('🔌 连接WebSocket...'));

    return new Promise((resolve, reject) => {
      this.socket = io(this.gatewayUrl, {
        auth: { token },
        transports: ['websocket'],
        timeout: 10000,
      });

      this.socket.on('connect', () => {
        console.log(chalk.green('✅ WebSocket连接成功'));
        resolve();
      });

      this.socket.on('connect_error', (error) => {
        console.log(chalk.red('❌ WebSocket连接失败:'), error.message);
        reject(error);
      });

      this.socket.on('disconnect', () => {
        console.log(chalk.yellow('🔌 WebSocket连接断开'));
      });
    });
  }



  /**
   * 发送WebSocket消息并等待响应
   */
  async sendMessage(command, payload = {}) {
    return new Promise((resolve, reject) => {
      const messageId = `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      const message = {
        id: messageId,
        command: command,
        payload: payload
      };

      console.log(chalk.gray(`📤 发送消息: ${command}`));
      console.log(chalk.gray(`📤 消息内容: ${JSON.stringify(message, null, 2)}`));

      let responseReceived = false;

      // 设置响应监听器 - 修复：监听'message'事件而不是'response'
      const responseHandler = (response) => {
        if (response.id === messageId && !responseReceived) {
          responseReceived = true;
          this.socket.off('message', responseHandler);
          clearTimeout(timeout);
          resolve(response);
        }
      };

      this.socket.on('message', responseHandler);

      // 设置超时
      const timeout = setTimeout(() => {
        if (!responseReceived) {
          responseReceived = true;
          this.socket.off('message', responseHandler);
          reject(new Error('WebSocket消息超时'));
        }
      }, 10000);

      // 发送消息
      this.socket.emit('message', message);
    });
  }

  /**
   * 测试通过WebSocket调用缓存装饰器
   */
  async testWebSocketCacheDecorators() {
    console.log(chalk.blue('\n🧪 测试WebSocket代理缓存装饰器'));
    
    const testKey = `ws-cache-test-${Date.now()}`;
    console.log(chalk.gray(`  📋 测试键: ${testKey}`));

    try {
      // 测试1: 调用带有@Cacheable装饰器的健康检查接口
      console.log(chalk.gray('  🔍 第一次调用（应该缓存未命中）...'));
      const firstCallStart = Date.now();
      const firstResponse = await this.sendMessage('auth.cache-expression-test', {
        testKey: testKey
      });
      const firstCallTime = Date.now() - firstCallStart;
      
      console.log(chalk.gray(`    响应时间: ${firstCallTime}ms`));
      const firstDataStr = JSON.stringify(firstResponse);
      console.log(chalk.gray(`    响应数据: ${firstDataStr.substring(0, 100)}...`));
      
      // 等待确保缓存生效
      await new Promise(resolve => setTimeout(resolve, 200));
      
      // 测试2: 第二次调用（应该缓存命中）
      console.log(chalk.gray('  🔍 第二次调用（应该缓存命中）...'));
      const secondCallStart = Date.now();
      const secondResponse = await this.sendMessage('auth.cache-expression-test', {
        testKey: testKey
      });
      const secondCallTime = Date.now() - secondCallStart;
      
      console.log(chalk.gray(`    响应时间: ${secondCallTime}ms`));
      const secondDataStr = JSON.stringify(secondResponse);
      console.log(chalk.gray(`    响应数据: ${secondDataStr.substring(0, 100)}...`));
      
      // 验证是否从缓存获取 - 修复：检查响应结构
      const firstData = firstResponse.payload?.data || firstResponse.data;
      const secondData = secondResponse.payload?.data || secondResponse.data;

      const dataMatches = JSON.stringify(firstData) === JSON.stringify(secondData);
      const hasRandomValue = firstData && firstData.randomValue !== undefined;
      const randomValueMatches = hasRandomValue &&
        firstData.randomValue === secondData.randomValue;
      
      console.log(chalk.cyan(`  📊 缓存分析:`));
      console.log(chalk.gray(`    数据匹配: ${dataMatches ? '是' : '否'}`));
      console.log(chalk.gray(`    随机值匹配: ${randomValueMatches ? '是' : '否'}`));
      console.log(chalk.gray(`    首次调用: ${firstCallTime}ms`));
      console.log(chalk.gray(`    第二次调用: ${secondCallTime}ms`));
      
      if (randomValueMatches) {
        console.log(chalk.green(`    状态: 缓存命中 ✅`));
      } else {
        console.log(chalk.red(`    状态: 缓存未命中 ❌`));
      }
      
      this.testResults.push({
        test: 'WebSocket缓存装饰器',
        firstCall: firstCallTime,
        secondCall: secondCallTime,
        cacheHit: randomValueMatches,
        dataMatches: dataMatches,
        status: randomValueMatches ? 'success' : 'failed'
      });
      
    } catch (error) {
      console.log(chalk.red(`❌ WebSocket缓存测试失败: ${error.message}`));
      this.testResults.push({
        test: 'WebSocket缓存装饰器',
        status: 'error',
        error: error.message
      });
    }
  }

  /**
   * 对比HTTP直接调用
   */
  async testHttpDirectCall() {
    console.log(chalk.blue('\n🧪 对比HTTP直接调用'));
    
    const axios = require('axios');
    const testKey = `http-cache-test-${Date.now()}`;
    
    try {
      // HTTP直接调用
      console.log(chalk.gray('  🔍 HTTP第一次调用...'));
      const firstCallStart = Date.now();
      const firstResponse = await axios.get(`http://127.0.0.1:3001/health/cache-expression-test/${testKey}`);
      const firstCallTime = Date.now() - firstCallStart;
      
      await new Promise(resolve => setTimeout(resolve, 200));
      
      console.log(chalk.gray('  🔍 HTTP第二次调用...'));
      const secondCallStart = Date.now();
      const secondResponse = await axios.get(`http://127.0.0.1:3001/health/cache-expression-test/${testKey}`);
      const secondCallTime = Date.now() - secondCallStart;
      
      const randomValueMatches = firstResponse.data.randomValue === secondResponse.data.randomValue;
      
      console.log(chalk.cyan(`  📊 HTTP缓存分析:`));
      console.log(chalk.gray(`    随机值匹配: ${randomValueMatches ? '是' : '否'}`));
      console.log(chalk.gray(`    首次调用: ${firstCallTime}ms`));
      console.log(chalk.gray(`    第二次调用: ${secondCallTime}ms`));
      
      if (randomValueMatches) {
        console.log(chalk.green(`    状态: 缓存命中 ✅`));
      } else {
        console.log(chalk.red(`    状态: 缓存未命中 ❌`));
      }
      
      this.testResults.push({
        test: 'HTTP直接调用',
        firstCall: firstCallTime,
        secondCall: secondCallTime,
        cacheHit: randomValueMatches,
        status: randomValueMatches ? 'success' : 'failed'
      });
      
    } catch (error) {
      console.log(chalk.red(`❌ HTTP测试失败: ${error.message}`));
      this.testResults.push({
        test: 'HTTP直接调用',
        status: 'error',
        error: error.message
      });
    }
  }

  /**
   * 生成测试报告
   */
  generateReport() {
    console.log(chalk.cyan('\n📊 WebSocket缓存测试报告'));
    console.log(chalk.cyan('='.repeat(60)));
    
    this.testResults.forEach(result => {
      const status = result.status === 'success' ? '✅' : 
                    result.status === 'failed' ? '❌' : '⚠️';
      
      console.log(chalk.blue(`\n📋 ${result.test}:`));
      console.log(`  ${status} 状态: ${result.status}`);
      
      if (result.error) {
        console.log(chalk.red(`  错误: ${result.error}`));
      } else if (result.firstCall !== undefined) {
        console.log(chalk.gray(`  首次调用: ${result.firstCall}ms`));
        console.log(chalk.gray(`  第二次调用: ${result.secondCall}ms`));
        console.log(chalk.gray(`  缓存命中: ${result.cacheHit ? '是' : '否'}`));
      }
    });
    
    // 结论
    const wsResult = this.testResults.find(r => r.test === 'WebSocket缓存装饰器');
    const httpResult = this.testResults.find(r => r.test === 'HTTP直接调用');
    
    console.log(chalk.cyan(`\n📈 测试结论:`));
    
    if (wsResult && httpResult) {
      if (wsResult.cacheHit && httpResult.cacheHit) {
        console.log(chalk.green('✅ WebSocket代理和HTTP直接调用都支持缓存装饰器'));
      } else if (!wsResult.cacheHit && httpResult.cacheHit) {
        console.log(chalk.red('❌ WebSocket代理不支持缓存装饰器，但HTTP直接调用支持'));
        console.log(chalk.yellow('💡 建议：WebSocket代理调用的方法需要使用手动缓存方式'));
      } else if (wsResult.cacheHit && !httpResult.cacheHit) {
        console.log(chalk.yellow('⚠️ 意外结果：WebSocket支持但HTTP不支持'));
      } else {
        console.log(chalk.red('❌ WebSocket代理和HTTP直接调用都不支持缓存装饰器'));
      }
    }
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log(chalk.cyan('🚀 启动WebSocket缓存测试'));
    console.log(chalk.cyan('='.repeat(60)));
    
    try {
      const token = await this.setupAuthentication();
      await this.connectWebSocket(token);
      await this.testWebSocketCacheDecorators();
      await this.testHttpDirectCall();
      this.generateReport();
      
    } catch (error) {
      console.error(chalk.red('❌ 测试执行失败:'), error.message);
    } finally {
      if (this.socket) {
        this.socket.disconnect();
      }
    }
  }
}

// 运行测试
const test = new WebSocketCacheTest();
test.runAllTests();
