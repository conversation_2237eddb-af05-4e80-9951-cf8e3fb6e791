// Auto-generated from MLSPlayer.json
// Generated at: 2025-07-20T12:56:05.247Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface MLSPlayerDefinition {
  teamId: number; // 队伍ID 例: 90101, 90102 (原: TeamID)
  id: number; // 唯一标识符 例: 1, 2
  heroName: string; // 英雄名称 例: 多切兹, 哈维尔-托马斯 (原: FootballerName)
  positionName: string; // 名称 例: 门将, 中后卫 (原: PositionName)
  position: number; // 位置 例: 1, 2 (原: Position)
}

// 字段映射：新字段名 -> 原始字段名
export const MLSPlayerFieldMappings = {
  teamId: 'TeamID',
  heroName: 'FootballerName',
  positionName: 'PositionName',
  position: 'Position',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const MLSPlayerReverseFieldMappings = {
  'TeamID': 'teamId',
  'FootballerName': 'heroName',
  'PositionName': 'positionName',
  'Position': 'position',
} as const;

export const MLSPlayerMeta = {
  tableName: 'MLSPlayer',
  dataFileName: 'MLSPlayer.json',
  primaryKey: 'id',
  searchFields: ['heroName', 'positionName'],
  fieldsCount: 5,
  requiredFields: ['teamId', 'id', 'heroName', 'positionName', 'position'],
  optionalFields: [],
  renamedFieldsCount: 4,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: MLSPlayerFieldMappings,
  reverseFieldMappings: MLSPlayerReverseFieldMappings,
} as const;

export type MLSPlayerConfigMeta = typeof MLSPlayerMeta;
export type MLSPlayerFieldMapping = typeof MLSPlayerFieldMappings;
export type MLSPlayerReverseFieldMapping = typeof MLSPlayerReverseFieldMappings;
