import { Injectable, Logger } from '@nestjs/common';
import { readFile } from 'fs/promises';
import { join } from 'path';
import * as AllInterfaces from '../interfaces';

/**
 * 配置加载器
 * 负责从文件系统加载JSON配置文件并进行字段映射
 */
@Injectable()
export class ConfigLoader {
  private readonly logger = new Logger(ConfigLoader.name);
  private readonly dataPath = join(process.cwd(), 'libs/game-config/src/data');
  private readonly metaCache = new Map<string, any>();

  /**
   * 加载指定表的所有配置数据
   */
  async loadConfig<T>(tableName: string): Promise<T[]> {
    try {
      const meta = this.getTableMeta(tableName);
      const filePath = join(this.dataPath, meta.dataFileName);
      
      this.logger.debug(`Loading config: ${tableName} from ${meta.dataFileName}`);
      
      // 读取JSON文件
      const fileContent = await readFile(filePath, 'utf-8');
      const rawData = JSON.parse(fileContent);
      
      if (!Array.isArray(rawData)) {
        throw new Error(`Invalid config format: ${tableName}, expected array`);
      }
      
      // 应用字段映射
      const mappedData = rawData.map(item => this.mapFields(item, meta));
      
      this.logger.debug(`Loaded ${mappedData.length} items for ${tableName}`);
      return mappedData as T[];
      
    } catch (error) {
      this.logger.error(`Failed to load config: ${tableName}`, error.stack);
      throw new ConfigLoadError(`Cannot load ${tableName}: ${error.message}`);
    }
  }

  /**
   * 根据ID加载单个配置项
   */
  async loadById<T>(tableName: string, id: number): Promise<T | null> {
    const configs = await this.loadConfig<T>(tableName);
    const meta = this.getTableMeta(tableName);
    
    const config = configs.find(item => item[meta.primaryKey] === id);
    return config || null;
  }

  /**
   * 批量加载配置项
   */
  async loadBatch<T>(tableName: string, ids: number[]): Promise<Map<number, T>> {
    const configs = await this.loadConfig<T>(tableName);
    const meta = this.getTableMeta(tableName);
    const result = new Map<number, T>();
    
    configs.forEach(config => {
      const id = config[meta.primaryKey];
      if (ids.includes(id)) {
        result.set(id, config);
      }
    });
    
    return result;
  }

  /**
   * 搜索配置项
   */
  async search<T>(tableName: string, keyword: string): Promise<T[]> {
    const configs = await this.loadConfig<T>(tableName);
    const meta = this.getTableMeta(tableName);
    
    if (!keyword || keyword.trim() === '') {
      return configs;
    }
    
    const searchKeyword = keyword.toLowerCase();
    
    return configs.filter(config => {
      // 在搜索字段中查找关键词
      return meta.searchFields.some(field => {
        const value = config[field];
        if (typeof value === 'string') {
          return value.toLowerCase().includes(searchKeyword);
        }
        if (typeof value === 'number') {
          return value.toString().includes(searchKeyword);
        }
        return false;
      });
    });
  }

  /**
   * 获取表的元数据
   */
  private getTableMeta(tableName: string): any {
    if (this.metaCache.has(tableName)) {
      return this.metaCache.get(tableName);
    }

    // 动态获取元数据
    const metaName = `${tableName}Meta`;
    const meta = AllInterfaces[metaName];
    
    if (!meta) {
      throw new Error(`Meta not found for table: ${tableName}`);
    }
    
    this.metaCache.set(tableName, meta);
    return meta;
  }

  /**
   * 应用字段映射
   */
  private mapFields<T>(rawData: any, meta: any): T {
    if (!meta.hasFieldMappings) {
      return rawData; // 无需映射，直接返回
    }
    
    const mapped: any = {};
    
    // 应用字段映射
    for (const [newField, originalField] of Object.entries(meta.fieldMappings)) {
      if (rawData.hasOwnProperty(originalField)) {
        (mapped as any)[newField] = (rawData as any)[originalField as string];
      }
    }
    
    // 处理未映射的字段（保持原样）
    for (const [key, value] of Object.entries(rawData)) {
      if (!Object.values(meta.fieldMappings).includes(key)) {
        mapped[key] = value;
      }
    }
    
    return mapped;
  }

  /**
   * 验证配置文件是否存在
   */
  async validateConfigFile(tableName: string): Promise<boolean> {
    try {
      const meta = this.getTableMeta(tableName);
      const filePath = join(this.dataPath, meta.dataFileName);
      await readFile(filePath, 'utf-8');
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 获取所有可用的表名
   */
  getAvailableTables(): string[] {
    const tableNames: string[] = [];
    
    // 从接口中提取所有表名
    Object.keys(AllInterfaces).forEach(key => {
      if (key.endsWith('Meta')) {
        const tableName = key.replace('Meta', '');
        tableNames.push(tableName);
      }
    });
    
    return tableNames.sort();
  }
}

/**
 * 配置加载错误
 */
export class ConfigLoadError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ConfigLoadError';
  }
}
