import { NestFactory, NestApplication } from '@nestjs/core';
import { MicroserviceOptions } from '@nestjs/microservices';
import { MicroserviceName } from '@shared/constants';

/**
 * 快速启动微服务的工具函数
 * 返回微服务实例，类型与 NestFactory.createMicroservice 一致
 *
 * @deprecated 请使用 ConfigService 获取配置，而不是使用此工具函数
 */
export async function bootstrapMicroservice(
  appModule: any,
  serviceName: MicroserviceName,
  customConfig?: Partial<MicroserviceOptions>
) {
  throw new Error('此函数已弃用，请使用 ConfigService 获取微服务配置');
}

/**
 * 快速启动混合应用（HTTP + 微服务）
 * 返回 HTTP 应用实例，类型与 NestFactory.create 一致
 *
 * @deprecated 请使用 ConfigService 获取配置，而不是使用此工具函数
 */
export async function bootstrapHybridApp(
  appModule: any,
  serviceName: MicroserviceName,
  httpPort: number = 3000,
  customConfig?: Partial<MicroserviceOptions>
) {
  throw new Error('此函数已弃用，请使用 ConfigService 获取微服务配置');
}

/**
 * 优雅关闭微服务
 */
export async function gracefulShutdown(app: any) {
  console.log('🛑 正在优雅关闭微服务...');
  
  // 设置关闭超时
  const shutdownTimeout = setTimeout(() => {
    console.error('❌ 关闭超时，强制退出');
    process.exit(1);
  }, 10000);

  try {
    await app.close();
    clearTimeout(shutdownTimeout);
    console.log('✅ 微服务已优雅关闭');
    process.exit(0);
  } catch (error) {
    console.error('❌ 关闭过程中发生错误:', error);
    clearTimeout(shutdownTimeout);
    process.exit(1);
  }
}

/**
 * 注册优雅关闭信号处理
 */
export function setupGracefulShutdown(app: any) {
  process.on('SIGTERM', () => gracefulShutdown(app));
  process.on('SIGINT', () => gracefulShutdown(app));
  process.on('SIGUSR2', () => gracefulShutdown(app)); // nodemon 重启信号
}
