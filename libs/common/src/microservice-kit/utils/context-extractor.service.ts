import { Injectable, Logger } from '@nestjs/common';

/**
 * 上下文提取器服务
 * 负责从调用数据中提取区服相关的上下文信息
 */
@Injectable()
export class ContextExtractorService {
  private readonly logger = new Logger(ContextExtractorService.name);

  /**
   * 从调用数据中提取区服ID
   * 基于现有的 ServerAwareMicroserviceClientService.extractServerIdFromData 优化
   */
  extractServerId(data?: any): string | null {
    if (!data) {
      return this.getDefaultServerId();
    }

    // 1. 直接从数据中提取
    if (data.serverId) {
      this.logger.debug(`从数据中提取区服ID: ${data.serverId}`);
      return data.serverId;
    }

    // 2. 从服务器上下文中提取
    if (data.serverContext?.serverId) {
      this.logger.debug(`从服务器上下文中提取区服ID: ${data.serverContext.serverId}`);
      return data.serverContext.serverId;
    }

    // 3. 从用户ID推断（如果有映射逻辑）
    if (data.userId || data.characterId) {
      const inferredServerId = this.inferServerIdFromUser(data.userId || data.characterId);
      if (inferredServerId) {
        this.logger.debug(`从用户ID推断区服ID: ${inferredServerId}`);
        return inferredServerId;
      }
    }

    // 4. 从HTTP请求头中提取
    const headerServerId = this.extractFromHeaders();
    if (headerServerId) {
      this.logger.debug(`从请求头中提取区服ID: ${headerServerId}`);
      return headerServerId;
    }

    // 5. 返回默认区服ID
    const defaultServerId = this.getDefaultServerId();
    if (defaultServerId) {
      this.logger.debug(`使用默认区服ID: ${defaultServerId}`);
    }
    return defaultServerId;
  }

  /**
   * 从HTTP请求头中提取区服ID
   */
  private extractFromHeaders(): string | null {
    try {
      // 尝试获取当前HTTP请求上下文
      const request = this.getCurrentRequest();
      if (request?.headers) {
        return request.headers['x-server-id'] || 
               request.headers['server-id'] || 
               null;
      }
    } catch (error) {
      // 不在HTTP请求上下文中，忽略错误
      this.logger.debug('无法获取HTTP请求上下文');
    }
    return null;
  }

  /**
   * 从用户ID推断区服ID
   * 可以根据业务逻辑实现用户到区服的映射
   */
  private inferServerIdFromUser(userId: string): string | null {
    if (!userId) return null;
    
    // 示例：根据用户ID哈希分配区服
    // 实际实现可以查询数据库或缓存
    try {
      const hash = this.simpleHash(userId);
      const serverIndex = hash % 3 + 1; // 假设有3个区服
      const serverId = `server${serverIndex.toString().padStart(3, '0')}`;
      
      this.logger.debug(`用户 ${userId} 哈希分配到区服: ${serverId}`);
      return serverId;
    } catch (error) {
      this.logger.warn(`用户ID哈希分配失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 获取默认区服ID
   */
  private getDefaultServerId(): string | null {
    return process.env.SERVER_ID || 
           process.env.DEFAULT_SERVER_ID || 
           null;
  }

  /**
   * 获取当前HTTP请求对象
   */
  private getCurrentRequest(): any {
    try {
      // 这里需要根据实际的NestJS上下文获取方式实现
      // 可能需要使用 @nestjs/core 的 REQUEST 注入
      // 临时返回null，实际实现时需要完善
      return null;
    } catch {
      return null;
    }
  }

  /**
   * 简单哈希函数
   */
  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash);
  }

  /**
   * 提取完整的调用上下文
   */
  extractCallContext(data?: any): CallContext {
    return {
      serverId: this.extractServerId(data),
      userId: data?.userId || data?.sub || null,
      characterId: data?.characterId || null,
      sessionId: data?.sessionId || null,
      requestId: this.generateRequestId(),
      timestamp: Date.now(),
    };
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 调用上下文接口
 */
export interface CallContext {
  serverId: string | null;
  userId: string | null;
  characterId: string | null;
  sessionId: string | null;
  requestId: string;
  timestamp: number;
}
