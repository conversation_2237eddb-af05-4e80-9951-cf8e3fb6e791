/**
 * Redis测试环境配置
 * 用于管理测试功能的环境隔离和安全控制
 */

export interface TestEnvironmentConfig {
  enableTestFeatures: boolean;
  redisKeyPrefix: string;
  redisDatabase: number;
  testLockTTL: number;
  maxTestConnections: number;
  testRateLimit: {
    windowMs: number;
    max: number;
  };
}

export interface EnvironmentSpecificConfig {
  redis: {
    host: string;
    port: number;
    password?: string;
    db: number;
    keyPrefix: string;
  };
  test: TestEnvironmentConfig;
  security: {
    allowTestFeatures: boolean;
    logTestAccess: boolean;
    alertOnProductionTestAccess: boolean;
  };
}

/**
 * 获取环境特定的配置
 */
export function getEnvironmentConfig(): EnvironmentSpecificConfig {
  const env = process.env.NODE_ENV || 'development';
  
  const baseConfig = {
    development: {
      redis: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT) || 6379,
        password: process.env.REDIS_PASSWORD,
        db: 0,
        keyPrefix: 'dev:',
      },
      test: {
        enableTestFeatures: true,
        redisKeyPrefix: 'test:',
        redisDatabase: 0,
        testLockTTL: 30, // 30秒默认TTL
        maxTestConnections: 100,
        testRateLimit: {
          windowMs: 60000, // 1分钟
          max: 100, // 最多100次请求
        },
      },
      security: {
        allowTestFeatures: true,
        logTestAccess: true,
        alertOnProductionTestAccess: false,
      },
    },
    
    test: {
      redis: {
        host: process.env.REDIS_TEST_HOST || 'localhost',
        port: parseInt(process.env.REDIS_TEST_PORT) || 6380,
        password: process.env.REDIS_TEST_PASSWORD || 'test123',
        db: 1, // 使用不同的数据库
        keyPrefix: 'test:',
      },
      test: {
        enableTestFeatures: true,
        redisKeyPrefix: 'test:',
        redisDatabase: 1,
        testLockTTL: 10, // 测试环境更短的TTL
        maxTestConnections: 50,
        testRateLimit: {
          windowMs: 60000,
          max: 200, // 测试环境允许更多请求
        },
      },
      security: {
        allowTestFeatures: true,
        logTestAccess: true,
        alertOnProductionTestAccess: false,
      },
    },
    
    production: {
      redis: {
        host: process.env.REDIS_HOST || 'redis',
        port: parseInt(process.env.REDIS_PORT) || 6379,
        password: process.env.REDIS_PASSWORD,
        db: 0,
        keyPrefix: 'prod:',
      },
      test: {
        enableTestFeatures: false, // 🔒 生产环境禁用测试功能
        redisKeyPrefix: 'prod:test:', // 即使有测试数据也要隔离
        redisDatabase: 0,
        testLockTTL: 0, // 不允许测试锁
        maxTestConnections: 0, // 不允许测试连接
        testRateLimit: {
          windowMs: 60000,
          max: 0, // 不允许测试请求
        },
      },
      security: {
        allowTestFeatures: false, // 🔒 严格禁止
        logTestAccess: true, // 记录所有测试访问尝试
        alertOnProductionTestAccess: true, // 生产环境测试访问告警
      },
    },
  };

  return baseConfig[env] || baseConfig.development;
}

/**
 * 检查是否允许测试功能
 */
export function isTestFeatureAllowed(): boolean {
  const config = getEnvironmentConfig();
  return config.test.enableTestFeatures && config.security.allowTestFeatures;
}

/**
 * 获取测试锁的键前缀
 */
export function getTestLockKeyPrefix(): string {
  const config = getEnvironmentConfig();
  return `${config.redis.keyPrefix}${config.test.redisKeyPrefix}lock:`;
}

/**
 * 获取测试配置
 */
export function getTestConfig(): TestEnvironmentConfig {
  const config = getEnvironmentConfig();
  return config.test;
}

/**
 * 检查是否应该记录测试访问
 */
export function shouldLogTestAccess(): boolean {
  const config = getEnvironmentConfig();
  return config.security.logTestAccess;
}

/**
 * 检查是否应该在生产环境测试访问时告警
 */
export function shouldAlertOnProductionTestAccess(): boolean {
  const config = getEnvironmentConfig();
  return config.security.alertOnProductionTestAccess;
}

/**
 * 验证环境配置
 */
export function validateEnvironmentConfig(): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const config = getEnvironmentConfig();
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // 检查生产环境安全性
  if (process.env.NODE_ENV === 'production') {
    if (config.test.enableTestFeatures) {
      errors.push('生产环境不应启用测试功能');
    }
    
    if (config.security.allowTestFeatures) {
      errors.push('生产环境不应允许测试功能');
    }
    
    if (!config.security.alertOnProductionTestAccess) {
      warnings.push('建议在生产环境启用测试访问告警');
    }
  }
  
  // 检查Redis配置
  if (!config.redis.host) {
    errors.push('Redis主机地址未配置');
  }
  
  if (!config.redis.port || config.redis.port < 1 || config.redis.port > 65535) {
    errors.push('Redis端口配置无效');
  }
  
  // 检查测试配置
  if (config.test.enableTestFeatures) {
    if (config.test.testLockTTL <= 0) {
      warnings.push('测试锁TTL应该大于0');
    }
    
    if (config.test.maxTestConnections <= 0) {
      warnings.push('最大测试连接数应该大于0');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * 环境配置常量
 */
export const ENV_CONFIG_CONSTANTS = {
  ENVIRONMENTS: ['development', 'test', 'production'] as const,
  DEFAULT_REDIS_PORT: 6379,
  DEFAULT_TEST_REDIS_PORT: 6380,
  DEFAULT_LOCK_TTL: 30,
  DEFAULT_RATE_LIMIT_WINDOW: 60000,
  DEFAULT_RATE_LIMIT_MAX: 100,
} as const;

/**
 * 环境配置类型
 */
export type Environment = typeof ENV_CONFIG_CONSTANTS.ENVIRONMENTS[number];

/**
 * 获取当前环境
 */
export function getCurrentEnvironment(): Environment {
  const env = process.env.NODE_ENV as Environment;
  return ENV_CONFIG_CONSTANTS.ENVIRONMENTS.includes(env) ? env : 'development';
}

/**
 * 是否为生产环境
 */
export function isProductionEnvironment(): boolean {
  return getCurrentEnvironment() === 'production';
}

/**
 * 是否为测试环境
 */
export function isTestEnvironment(): boolean {
  return getCurrentEnvironment() === 'test';
}

/**
 * 是否为开发环境
 */
export function isDevelopmentEnvironment(): boolean {
  return getCurrentEnvironment() === 'development';
}

/**
 * 获取环境特定的Redis配置
 */
export function getRedisConfig() {
  const config = getEnvironmentConfig();
  return config.redis;
}

/**
 * 创建环境安全的锁键
 */
export function createSafeLockKey(baseKey: string): string {
  const config = getEnvironmentConfig();
  return `${config.redis.keyPrefix}lock:${baseKey}`;
}

/**
 * 创建测试专用的锁键
 */
export function createTestLockKey(baseKey: string): string {
  if (!isTestFeatureAllowed()) {
    throw new Error('测试功能在当前环境中不可用');
  }
  
  const prefix = getTestLockKeyPrefix();
  return `${prefix}${baseKey}`;
}
