import { Test, TestingModule } from '@nestjs/testing';
import { CacheManagerService } from '../cache/cache-manager.service';
import { RedisService } from '../redis.service';
import { RedisProtectionService } from '../redis-protection.service';
import { RedisPubSubService } from '../redis-pubsub.service';
import { MockRedisClient, MockDataSource, SilentLogger, TestDataGenerator } from './test-utils';

describe('CacheManagerService', () => {
  let service: CacheManagerService;
  let redisService: RedisService;
  let protectionService: RedisProtectionService;
  let pubSubService: RedisPubSubService;
  let mockClient: MockRedisClient;

  beforeEach(async () => {
    mockClient = new MockRedisClient();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CacheManagerService,
        {
          provide: RedisService,
          useValue: {
            getClient: () => mockClient,
            get: jest.fn(),
            set: jest.fn(),
            del: jest.fn(),
            exists: jest.fn(),
            llen: jest.fn(),
            lrange: jest.fn(),
            lpush: jest.fn(),
          },
        },
        {
          provide: RedisProtectionService,
          useValue: {
            setProtected: jest.fn(),
          },
        },
        {
          provide: RedisPubSubService,
          useValue: {
            publishEvent: jest.fn(),
            subscribeToEvent: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<CacheManagerService>(CacheManagerService);
    redisService = module.get<RedisService>(RedisService);
    protectionService = module.get<RedisProtectionService>(RedisProtectionService);
    pubSubService = module.get<RedisPubSubService>(RedisPubSubService);
    
    // 使用静默日志记录器
    (service as any).logger = new SilentLogger();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('存储库管理', () => {
    it('应该能够创建缓存存储库', () => {
      const repositoryName = 'test-repo';

      const repository = service.createRepository(repositoryName);

      expect(repository).toBeDefined();
      expect(service.hasRepository(repositoryName)).toBe(true);
    });

    it('应该能够获取已存在的存储库', () => {
      const repositoryName = 'existing-repo';

      const repository1 = service.createRepository(repositoryName);
      const repository2 = service.getRepository(repositoryName);

      expect(repository1).toBe(repository2);
    });

    it('应该能够注册数据源', () => {
      const dataSourceName = 'test-datasource';
      const mockDataSource = new MockDataSource({ 'test-key': 'test-value' });

      service.registerDataSource(dataSourceName, mockDataSource);

      // 创建使用该数据源的存储库
      const repository = service.createRepository(dataSourceName);
      expect(repository).toBeDefined();
    });

    it('应该能够移除存储库', () => {
      const repositoryName = 'removable-repo';

      service.createRepository(repositoryName);
      expect(service.hasRepository(repositoryName)).toBe(true);

      const removed = service.removeRepository(repositoryName);
      expect(removed).toBe(true);
      expect(service.hasRepository(repositoryName)).toBe(false);
    });

    it('应该返回存储库名称列表', () => {
      const repoNames = ['repo1', 'repo2', 'repo3'];

      repoNames.forEach(name => service.createRepository(name));

      const names = service.getRepositoryNames();
      expect(names).toEqual(expect.arrayContaining(repoNames));
    });
  });

  describe('缓存操作', () => {
    it('应该能够批量获取缓存数据', async () => {
      const repositoryName = 'batch-get-repo';
      const keys = ['key1', 'key2', 'key3'];

      jest.spyOn(redisService, 'get')
        .mockResolvedValueOnce('value1')
        .mockResolvedValueOnce(null)
        .mockResolvedValueOnce('value3');

      const results = await service.batchGet(repositoryName, keys);

      expect(results.size).toBe(3);
      expect(results.get('key1')?.hit).toBe(true);
      expect(results.get('key1')?.data).toBe('value1');
      expect(results.get('key2')?.hit).toBe(false);
      expect(results.get('key3')?.hit).toBe(true);
    });

    it('应该能够批量设置缓存数据', async () => {
      const repositoryName = 'batch-set-repo';
      const entries = new Map([
        ['key1', 'value1'],
        ['key2', 'value2'],
        ['key3', 'value3'],
      ]);

      jest.spyOn(redisService, 'set').mockResolvedValue('OK');

      await service.batchSet(repositoryName, entries, { ttl: 3600 });

      expect(redisService.set).toHaveBeenCalledTimes(3);
    });

    it('应该能够清理所有缓存', async () => {
      const repoNames = ['repo1', 'repo2', 'repo3'];

      // 创建存储库
      repoNames.forEach(name => service.createRepository(name));

      jest.spyOn(redisService.getClient(), 'keys').mockResolvedValue(['cache:repo1:key1', 'cache:repo2:key2']);
      jest.spyOn(redisService, 'del').mockResolvedValue(1);

      await service.clearAll();

      expect(redisService.del).toHaveBeenCalled();
    });
  });

  describe('缓存预热', () => {
    it('应该能够预热存储库', async () => {
      const repositoryName = 'warmup-repo';
      const keys = ['key1', 'key2', 'key3'];
      const mockDataSource = new MockDataSource({
        'key1': 'value1',
        'key2': 'value2',
        'key3': 'value3',
      });

      service.registerDataSource(repositoryName, mockDataSource);
      service.createRepository(repositoryName, mockDataSource);

      jest.spyOn(redisService, 'get').mockResolvedValue(null); // 缓存未命中
      jest.spyOn(redisService, 'set').mockResolvedValue('OK');

      await service.warmupRepository(repositoryName, keys);

      expect(redisService.set).toHaveBeenCalledTimes(3);
      expect(mockDataSource.getLoadCount()).toBe(3);
    });

    it('应该能够预热所有存储库', async () => {
      const repoNames = ['repo1', 'repo2'];

      repoNames.forEach(name => {
        const mockDataSource = new MockDataSource({ [`${name}-key`]: `${name}-value` });
        service.registerDataSource(name, mockDataSource);
        service.createRepository(name, mockDataSource);
      });

      jest.spyOn(service, 'warmupRepository').mockResolvedValue();

      await service.warmupAll();

      expect(service.warmupRepository).toHaveBeenCalledTimes(2);
    });
  });

  describe('统计信息', () => {
    it('应该返回全局统计信息', async () => {
      const repositoryName = 'stats-repo';
      const repository = service.createRepository(repositoryName);

      // 模拟一些统计数据
      (repository as any).stats = {
        requests: 100,
        hits: 80,
        misses: 20,
        hitRate: 0.8,
        avgLoadTime: 50,
      };

      const stats = service.getStats();

      expect(stats.totalRequests).toBe(100);
      expect(stats.hits).toBe(80);
      expect(stats.misses).toBe(20);
      expect(stats.hitRate).toBe(0.8);
      expect(stats.repositories[repositoryName]).toBeDefined();
    });

    it('应该能够重置所有统计信息', () => {
      const repositoryName = 'reset-stats-repo';
      const repository = service.createRepository(repositoryName);

      // 模拟统计数据
      (repository as any).stats = {
        requests: 100,
        hits: 80,
        misses: 20,
      };

      service.resetAllStats();

      // 验证统计被重置
      const stats = service.getStats();
      expect(stats.totalRequests).toBe(0);
    });
  });

  describe('事件监听', () => {
    it('应该能够添加和移除事件监听器', () => {
      const listener = {
        onCacheHit: jest.fn(),
        onCacheMiss: jest.fn(),
      };

      service.addListener(listener);
      expect((service as any).listeners).toContain(listener);

      service.removeListener(listener);
      expect((service as any).listeners).not.toContain(listener);
    });

    it('应该能够处理缓存事件', async () => {
      const listener = {
        onCacheHit: jest.fn(),
        onCacheMiss: jest.fn(),
        onCacheError: jest.fn(),
      };

      service.addListener(listener);

      // 模拟缓存事件
      const hitEvent = {
        type: 'hit' as const,
        repository: 'test-repo',
        key: 'test-key',
        data: 'test-data',
        timestamp: new Date(),
      };

      const missEvent = {
        type: 'miss' as const,
        repository: 'test-repo',
        key: 'test-key',
        data: null,
        timestamp: new Date(),
      };

      const errorEvent = {
        type: 'error' as const,
        repository: 'test-repo',
        key: 'test-key',
        error: new Error('Test error'),
        timestamp: new Date(),
      };

      // 触发事件处理
      (service as any).handleCacheEvent(hitEvent);
      (service as any).handleCacheEvent(missEvent);
      (service as any).handleCacheEvent(errorEvent);

      expect(listener.onCacheHit).toHaveBeenCalledWith(hitEvent);
      expect(listener.onCacheMiss).toHaveBeenCalledWith(missEvent);
      expect(listener.onCacheError).toHaveBeenCalledWith(errorEvent);
    });
  });

  describe('Cache-Aside 模式', () => {
    it('应该在缓存未命中时从数据源加载', async () => {
      const repositoryName = 'cache-aside-repo';
      const key = 'test-key';
      const value = 'test-value';
      const mockDataSource = new MockDataSource({ [key]: value });

      service.registerDataSource(repositoryName, mockDataSource);
      const repository = service.createRepository(repositoryName, mockDataSource);

      jest.spyOn(redisService, 'get').mockResolvedValue(null); // 缓存未命中
      jest.spyOn(redisService, 'set').mockResolvedValue('OK');

      const result = await repository.getOrLoad(key, () => mockDataSource.load(key));

      expect(result).toBe(value);
      expect(mockDataSource.getLoadCount()).toBe(1);
      expect(redisService.set).toHaveBeenCalled();
    });

    it('应该在缓存命中时直接返回', async () => {
      const repositoryName = 'cache-hit-repo';
      const key = 'cached-key';
      const cachedValue = 'cached-value';
      const mockDataSource = new MockDataSource();

      service.registerDataSource(repositoryName, mockDataSource);
      const repository = service.createRepository(repositoryName, mockDataSource);

      jest.spyOn(redisService, 'get').mockResolvedValue(cachedValue); // 缓存命中

      const result = await repository.getOrLoad(key, () => mockDataSource.load(key));

      expect(result).toBe(cachedValue);
      expect(mockDataSource.getLoadCount()).toBe(0); // 不应该调用数据源
    });
  });

  describe('Write-Through 模式', () => {
    it('应该同时写入缓存和数据源', async () => {
      const repositoryName = 'write-through-repo';
      const key = 'write-key';
      const value = 'write-value';
      const mockDataSource = new MockDataSource();

      service.registerDataSource(repositoryName, mockDataSource);
      const repository = service.createRepository(repositoryName, mockDataSource);

      jest.spyOn(redisService, 'set').mockResolvedValue('OK');

      await repository.setThrough(key, value);

      expect(mockDataSource.getSaveCount()).toBe(1);
      expect(redisService.set).toHaveBeenCalled();
    });
  });

  describe('Write-Behind 模式', () => {
    it('应该立即写入缓存并异步写入数据源', async () => {
      const repositoryName = 'write-behind-repo';
      const key = 'async-key';
      const value = 'async-value';
      const mockDataSource = new MockDataSource();

      service.registerDataSource(repositoryName, mockDataSource);
      const repository = service.createRepository(repositoryName, mockDataSource);

      jest.spyOn(redisService, 'set').mockResolvedValue('OK');
      jest.spyOn(pubSubService, 'publishEvent').mockResolvedValue(1);

      await repository.setBehind(key, value);

      expect(redisService.set).toHaveBeenCalled();
      expect(pubSubService.publishEvent).toHaveBeenCalledWith(
        'cache',
        'async-write',
        expect.objectContaining({
          repository: repositoryName,
          key,
          data: value,
        })
      );
    });
  });

  describe('错误处理', () => {
    it('应该处理数据源加载错误', async () => {
      const repositoryName = 'error-repo';
      const key = 'error-key';
      const mockDataSource = new MockDataSource();
      mockDataSource.setShouldFail(true);

      service.registerDataSource(repositoryName, mockDataSource);
      const repository = service.createRepository(repositoryName, mockDataSource);

      jest.spyOn(redisService, 'get').mockResolvedValue(null);

      await expect(repository.getOrLoad(key, () => mockDataSource.load(key)))
        .rejects.toThrow('Mock data source load failed');
    });

    it('应该处理缓存写入错误', async () => {
      const repositoryName = 'cache-error-repo';
      const key = 'cache-error-key';
      const value = 'cache-error-value';
      const mockDataSource = new MockDataSource({ [key]: value });

      service.registerDataSource(repositoryName, mockDataSource);
      const repository = service.createRepository(repositoryName, mockDataSource);

      jest.spyOn(redisService, 'get').mockResolvedValue(null);
      jest.spyOn(redisService, 'set').mockRejectedValue(new Error('Cache write failed'));

      // 缓存写入失败不应该影响数据返回
      const result = await repository.getOrLoad(key, () => mockDataSource.load(key));

      expect(result).toBe(value);
      expect(mockDataSource.getLoadCount()).toBe(1);
    });

    it('应该处理不存在的存储库', () => {
      expect(() => service.getRepository('non-existent-repo')).not.toThrow();
    });
  });

  describe('配置和初始化', () => {
    it('应该正确初始化默认配置', () => {
      const config = (service as any).getDefaultConfig();

      expect(config.defaultTTL).toBe(3600);
      expect(config.enableProtection).toBe(true);
      expect(config.enableStats).toBe(true);
      expect(config.enableEvents).toBe(true);
    });

    it('应该在模块初始化时订阅事件', async () => {
      jest.spyOn(pubSubService, 'subscribeToEvent').mockResolvedValue();

      await service.onModuleInit();

      expect(pubSubService.subscribeToEvent).toHaveBeenCalledWith(
        'cache',
        '*',
        expect.any(Function)
      );
    });
  });
});
