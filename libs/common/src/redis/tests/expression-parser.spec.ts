import { ExpressionParser, ExpressionContext } from '../cache/expression-parser';

describe('ExpressionParser', () => {
  let context: ExpressionContext;

  beforeEach(() => {
    context = {
      args: ['user123', 'admin', { name: '<PERSON>', age: 30 }],
      paramNames: ['userId', 'userType', 'userData'],
      result: { id: 'user123', username: 'john_doe', isPrivate: false, profile: { type: 'premium' } },
      target: { serviceName: 'UserService' },
      methodName: 'findUser'
    };
  });

  describe('parse', () => {
    it('应该解析简单的参数引用', () => {
      const template = 'user:#{userId}';
      const result = ExpressionParser.parse(template, context);
      expect(result).toBe('user:user123');
    });

    it('应该解析多个参数引用', () => {
      const template = 'user:#{userId}:#{userType}';
      const result = ExpressionParser.parse(template, context);
      expect(result).toBe('user:user123:admin');
    });

    it('应该解析返回值属性引用', () => {
      const template = 'user:#{result.username}';
      const result = ExpressionParser.parse(template, context);
      expect(result).toBe('user:john_doe');
    });

    it('应该解析嵌套属性引用', () => {
      const template = 'user:#{result.profile.type}';
      const result = ExpressionParser.parse(template, context);
      expect(result).toBe('user:premium');
    });

    it('应该解析目标对象属性', () => {
      const template = 'service:#{target.serviceName}';
      const result = ExpressionParser.parse(template, context);
      expect(result).toBe('service:UserService');
    });

    it('应该解析方法名引用', () => {
      const template = 'cache:#{methodName}:#{userId}';
      const result = ExpressionParser.parse(template, context);
      expect(result).toBe('cache:findUser:user123');
    });

    it('应该处理复杂对象参数', () => {
      const template = 'user:#{userData.name}:#{userData.age}';
      const result = ExpressionParser.parse(template, context);
      expect(result).toBe('user:John:30');
    });

    it('应该处理空值', () => {
      const contextWithNull = {
        ...context,
        args: [null, undefined, ''],
        paramNames: ['nullParam', 'undefinedParam', 'emptyParam']
      };
      
      expect(ExpressionParser.parse('#{nullParam}', contextWithNull)).toBe('null');
      expect(ExpressionParser.parse('#{undefinedParam}', contextWithNull)).toBe('null');
      expect(ExpressionParser.parse('#{emptyParam}', contextWithNull)).toBe('');
    });

    it('应该处理不存在的参数', () => {
      const template = 'user:#{nonExistentParam}';
      // 不存在的参数应该保持原样或抛出错误
      expect(() => ExpressionParser.parse(template, context)).toThrow();
    });

    it('应该处理无效的表达式', () => {
      const template = 'user:#{invalid.}';
      expect(() => ExpressionParser.parse(template, context)).toThrow();
    });

    it('应该处理空模板', () => {
      expect(ExpressionParser.parse('', context)).toBe('');
      expect(ExpressionParser.parse(null as any, context)).toBe(null);
      expect(ExpressionParser.parse(undefined as any, context)).toBe(undefined);
    });

    it('应该处理没有表达式的模板', () => {
      const template = 'user:static:key';
      const result = ExpressionParser.parse(template, context);
      expect(result).toBe('user:static:key');
    });
  });

  describe('parseArray', () => {
    it('应该解析表达式数组', () => {
      const templates = [
        'user:#{userId}',
        'profile:#{result.username}',
        'cache:#{methodName}'
      ];
      const results = ExpressionParser.parseArray(templates, context);
      expect(results).toEqual([
        'user:user123',
        'profile:john_doe',
        'cache:findUser'
      ]);
    });

    it('应该处理空数组', () => {
      const results = ExpressionParser.parseArray([], context);
      expect(results).toEqual([]);
    });

    it('应该处理包含无效表达式的数组', () => {
      const templates = ['user:#{userId}', 'invalid:#{nonExistent}'];
      expect(() => ExpressionParser.parseArray(templates, context)).toThrow();
    });
  });

  describe('evaluateCondition', () => {
    it('应该评估简单的非空条件', () => {
      expect(ExpressionParser.evaluateCondition('#{userId != null}', context)).toBe(true);
      
      const nullContext = { ...context, args: [null], paramNames: ['userId'] };
      expect(ExpressionParser.evaluateCondition('#{userId != null}', nullContext)).toBe(false);
    });

    it('应该评估空值条件', () => {
      const nullContext = { ...context, args: [null], paramNames: ['userId'] };
      expect(ExpressionParser.evaluateCondition('#{userId == null}', nullContext)).toBe(true);
      expect(ExpressionParser.evaluateCondition('#{userId == null}', context)).toBe(false);
    });

    it('应该评估字符串相等条件', () => {
      expect(ExpressionParser.evaluateCondition('#{userType == "admin"}', context)).toBe(true);
      expect(ExpressionParser.evaluateCondition('#{userType == "user"}', context)).toBe(false);
    });

    it('应该评估布尔值条件', () => {
      expect(ExpressionParser.evaluateCondition('#{result.isPrivate}', context)).toBe(false);
      
      const privateContext = {
        ...context,
        result: { ...context.result, isPrivate: true }
      };
      expect(ExpressionParser.evaluateCondition('#{result.isPrivate}', privateContext)).toBe(true);
    });

    it('应该处理简单的存在性检查', () => {
      expect(ExpressionParser.evaluateCondition('#{userId}', context)).toBe(true);
      
      const emptyContext = { ...context, args: [''], paramNames: ['userId'] };
      expect(ExpressionParser.evaluateCondition('#{userId}', emptyContext)).toBe(false);
    });

    it('应该处理#{expression}格式', () => {
      expect(ExpressionParser.evaluateCondition('#{userId != null}', context)).toBe(true);
    });

    it('应该处理直接表达式格式', () => {
      expect(ExpressionParser.evaluateCondition('userId != null', context)).toBe(true);
    });

    it('应该处理空条件', () => {
      expect(ExpressionParser.evaluateCondition('', context)).toBe(true);
      expect(ExpressionParser.evaluateCondition(null as any, context)).toBe(true);
      expect(ExpressionParser.evaluateCondition(undefined as any, context)).toBe(true);
    });

    it('应该处理无效条件', () => {
      // 无效条件应该默认返回true，避免阻塞缓存
      expect(ExpressionParser.evaluateCondition('#{invalid.expression}', context)).toBe(true);
    });
  });

  describe('extractParameterNames', () => {
    it('应该从简单函数中提取参数名', () => {
      const func = function(userId: string, userType: string) { return null; };
      const paramNames = ExpressionParser.extractParameterNames(func);
      expect(paramNames).toEqual(['userId', 'userType']);
    });

    it('应该从箭头函数中提取参数名', () => {
      const func = (id: string, data: any) => null;
      const paramNames = ExpressionParser.extractParameterNames(func);
      expect(paramNames).toEqual(['id', 'data']);
    });

    it('应该处理无参数函数', () => {
      const func = () => null;
      const paramNames = ExpressionParser.extractParameterNames(func);
      expect(paramNames).toEqual([]);
    });

    it('应该处理带默认值的参数', () => {
      const func = function(id: string, type = 'user') { return null; };
      const paramNames = ExpressionParser.extractParameterNames(func);
      expect(paramNames).toEqual(['id', 'type']);
    });

    it('应该处理带类型注解的参数', () => {
      const func = function(id: string, data: UserData) { return null; };
      const paramNames = ExpressionParser.extractParameterNames(func);
      expect(paramNames).toEqual(['id', 'data']);
    });

    it('应该过滤剩余参数', () => {
      const func = function(id: string, ...args: any[]) { return null; };
      const paramNames = ExpressionParser.extractParameterNames(func);
      expect(paramNames).toEqual(['id']);
    });

    it('应该处理解析错误', () => {
      const invalidFunc = 'not a function' as any;
      const paramNames = ExpressionParser.extractParameterNames(invalidFunc);
      expect(paramNames).toEqual([]);
    });
  });

  describe('serializeValue', () => {
    it('应该序列化字符串', () => {
      const context = {
        args: ['test'],
        paramNames: ['str'],
        result: undefined,
        target: undefined,
        methodName: undefined
      };
      const result = ExpressionParser.parse('#{str}', context);
      expect(result).toBe('test');
    });

    it('应该序列化数字', () => {
      const context = {
        args: [123],
        paramNames: ['num'],
        result: undefined,
        target: undefined,
        methodName: undefined
      };
      const result = ExpressionParser.parse('#{num}', context);
      expect(result).toBe('123');
    });

    it('应该序列化布尔值', () => {
      const context = {
        args: [true, false],
        paramNames: ['bool1', 'bool2'],
        result: undefined,
        target: undefined,
        methodName: undefined
      };
      expect(ExpressionParser.parse('#{bool1}', context)).toBe('true');
      expect(ExpressionParser.parse('#{bool2}', context)).toBe('false');
    });

    it('应该序列化对象', () => {
      const context = {
        args: [{ name: 'John', age: 30 }],
        paramNames: ['obj'],
        result: undefined,
        target: undefined,
        methodName: undefined
      };
      const result = ExpressionParser.parse('#{obj}', context);
      expect(result).toBe('{"name":"John","age":30}');
    });

    it('应该处理循环引用对象', () => {
      const obj: any = { name: 'John' };
      obj.self = obj; // 创建循环引用
      
      const context = {
        args: [obj],
        paramNames: ['obj'],
        result: undefined,
        target: undefined,
        methodName: undefined
      };
      
      // 应该fallback到String()
      const result = ExpressionParser.parse('#{obj}', context);
      expect(result).toBe('[object Object]');
    });
  });

  describe('边界情况测试', () => {
    it('应该处理特殊字符', () => {
      const context = {
        args: ['<EMAIL>', 'user-name_123'],
        paramNames: ['email', 'username'],
        result: undefined,
        target: undefined,
        methodName: undefined
      };
      
      expect(ExpressionParser.parse('#{email}', context)).toBe('<EMAIL>');
      expect(ExpressionParser.parse('#{username}', context)).toBe('user-name_123');
    });

    it('应该处理Unicode字符', () => {
      const context = {
        args: ['用户123', '🎮游戏'],
        paramNames: ['chinese', 'emoji'],
        result: undefined,
        target: undefined,
        methodName: undefined
      };
      
      expect(ExpressionParser.parse('#{chinese}', context)).toBe('用户123');
      expect(ExpressionParser.parse('#{emoji}', context)).toBe('🎮游戏');
    });

    it('应该处理大型对象', () => {
      const largeObj = {};
      for (let i = 0; i < 1000; i++) {
        (largeObj as any)[`prop${i}`] = `value${i}`;
      }
      
      const context = {
        args: [largeObj],
        paramNames: ['largeObj'],
        result: undefined,
        target: undefined,
        methodName: undefined
      };
      
      const result = ExpressionParser.parse('#{largeObj}', context);
      expect(result).toContain('prop0');
      expect(result).toContain('value0');
    });

    it('应该处理深度嵌套对象', () => {
      const deepObj = {
        level1: {
          level2: {
            level3: {
              level4: {
                value: 'deep_value'
              }
            }
          }
        }
      };
      
      const context = {
        args: [deepObj],
        paramNames: ['deepObj'],
        result: undefined,
        target: undefined,
        methodName: undefined
      };
      
      const result = ExpressionParser.parse('#{deepObj.level1.level2.level3.level4.value}', context);
      expect(result).toBe('deep_value');
    });
  });
});
