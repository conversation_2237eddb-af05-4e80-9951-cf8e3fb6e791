import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '../redis.service';

// Mock Redis
const mockRedis = {
  set: jest.fn(),
  get: jest.fn(),
  del: jest.fn(),
  exists: jest.fn(),
  expire: jest.fn(),
  ttl: jest.fn(),
  hset: jest.fn(),
  hget: jest.fn(),
  hgetall: jest.fn(),
  hdel: jest.fn(),
  lpush: jest.fn(),
  rpop: jest.fn(),
  llen: jest.fn(),
  lrange: jest.fn(),
  zadd: jest.fn(),
  zrange: jest.fn(),
  zrevrange: jest.fn(),
  zscore: jest.fn(),
  zrem: jest.fn(),
  ping: jest.fn(),
  info: jest.fn(),
  duplicate: jest.fn(),
  on: jest.fn(),
  quit: jest.fn(),
};

// Mock ConfigService
const mockConfigService = {
  get: jest.fn((key: string, defaultValue?: any) => {
    const config = {
      REDIS_HOST: 'localhost',
      REDIS_PORT: 6379,
      REDIS_PASSWORD: 'test-password',
      REDIS_CLUSTER: false,
      REDIS_KEY_PREFIX: 'test:',
    };
    return config[key] || defaultValue;
  }),
};

describe('RedisService', () => {
  let service: RedisService;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RedisService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<RedisService>(RedisService);
    configService = module.get<ConfigService>(ConfigService);

    // Mock Redis instance
    (service as any).redis = mockRedis;
    (service as any).subscriber = mockRedis;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Operations', () => {
    it('should be defined', () => {
      expect(service).toBeDefined();
    });

    it('should set a value', async () => {
      const key = 'test:key';
      const value = { test: 'data' };
      const ttl = 3600;

      mockRedis.set.mockResolvedValue('OK');

      await service.set(key, value, ttl);

      expect(mockRedis.set).toHaveBeenCalledWith(key, JSON.stringify(value), 'EX', ttl);
    });

    it('should get a value', async () => {
      const key = 'test:key';
      const value = { test: 'data' };

      mockRedis.get.mockResolvedValue(JSON.stringify(value));

      const result = await service.get(key);

      expect(mockRedis.get).toHaveBeenCalledWith(key);
      expect(result).toEqual(value);
    });

    it('should return null for non-existent key', async () => {
      const key = 'non-existent';

      mockRedis.get.mockResolvedValue(null);

      const result = await service.get(key);

      expect(result).toBeNull();
    });

    it('should delete a key', async () => {
      const key = 'test:key';

      mockRedis.del.mockResolvedValue(1);

      const result = await service.del(key);

      expect(mockRedis.del).toHaveBeenCalledWith(key);
      expect(result).toBe(1);
    });

    it('should check if key exists', async () => {
      const key = 'test:key';

      mockRedis.exists.mockResolvedValue(1);

      const result = await service.exists(key);

      expect(mockRedis.exists).toHaveBeenCalledWith(key);
      expect(result).toBe(true);
    });

    it('should set expiration', async () => {
      const key = 'test:key';
      const seconds = 3600;

      mockRedis.expire.mockResolvedValue(1);

      const result = await service.expire(key, seconds);

      expect(mockRedis.expire).toHaveBeenCalledWith(key, seconds);
      expect(result).toBe(true);
    });

    it('should get TTL', async () => {
      const key = 'test:key';
      const ttl = 3600;

      mockRedis.ttl.mockResolvedValue(ttl);

      const result = await service.ttl(key);

      expect(mockRedis.ttl).toHaveBeenCalledWith(key);
      expect(result).toBe(ttl);
    });
  });

  describe('Hash Operations', () => {
    it('should set hash field', async () => {
      const key = 'test:hash';
      const field = 'field1';
      const value = { data: 'test' };

      mockRedis.hset.mockResolvedValue(1);

      await service.hset(key, field, value);

      expect(mockRedis.hset).toHaveBeenCalledWith(key, field, JSON.stringify(value));
    });

    it('should get hash field', async () => {
      const key = 'test:hash';
      const field = 'field1';
      const value = { data: 'test' };

      mockRedis.hget.mockResolvedValue(JSON.stringify(value));

      const result = await service.hget(key, field);

      expect(mockRedis.hget).toHaveBeenCalledWith(key, field);
      expect(result).toEqual(value);
    });

    it('should get all hash fields', async () => {
      const key = 'test:hash';
      const hash = {
        field1: JSON.stringify({ data: 'test1' }),
        field2: JSON.stringify({ data: 'test2' }),
      };

      mockRedis.hgetall.mockResolvedValue(hash);

      const result = await service.hgetall(key);

      expect(mockRedis.hgetall).toHaveBeenCalledWith(key);
      expect(result).toEqual({
        field1: { data: 'test1' },
        field2: { data: 'test2' },
      });
    });

    it('should delete hash fields', async () => {
      const key = 'test:hash';
      const fields = ['field1', 'field2'];

      mockRedis.hdel.mockResolvedValue(2);

      const result = await service.hdel(key, ...fields);

      expect(mockRedis.hdel).toHaveBeenCalledWith(key, ...fields);
      expect(result).toBe(2);
    });
  });

  describe('List Operations', () => {
    it('should push to list', async () => {
      const key = 'test:list';
      const values = [{ data: 'test1' }, { data: 'test2' }];

      mockRedis.lpush.mockResolvedValue(2);

      const result = await service.lpush(key, ...values);

      expect(mockRedis.lpush).toHaveBeenCalledWith(
        key,
        JSON.stringify(values[0]),
        JSON.stringify(values[1])
      );
      expect(result).toBe(2);
    });

    it('should pop from list', async () => {
      const key = 'test:list';
      const value = { data: 'test' };

      mockRedis.rpop.mockResolvedValue(JSON.stringify(value));

      const result = await service.rpop(key);

      expect(mockRedis.rpop).toHaveBeenCalledWith(key);
      expect(result).toEqual(value);
    });

    it('should get list length', async () => {
      const key = 'test:list';

      mockRedis.llen.mockResolvedValue(5);

      const result = await service.llen(key);

      expect(mockRedis.llen).toHaveBeenCalledWith(key);
      expect(result).toBe(5);
    });

    it('should get list range', async () => {
      const key = 'test:list';
      const values = [JSON.stringify({ data: 'test1' }), JSON.stringify({ data: 'test2' })];

      mockRedis.lrange.mockResolvedValue(values);

      const result = await service.lrange(key, 0, -1);

      expect(mockRedis.lrange).toHaveBeenCalledWith(key, 0, -1);
      expect(result).toEqual([{ data: 'test1' }, { data: 'test2' }]);
    });
  });

  describe('Sorted Set Operations', () => {
    it('should add to sorted set', async () => {
      const key = 'test:zset';
      const score = 100;
      const member = 'member1';

      mockRedis.zadd.mockResolvedValue(1);

      const result = await service.zadd(key, score, member);

      expect(mockRedis.zadd).toHaveBeenCalledWith(key, score, member);
      expect(result).toBe(1);
    });

    it('should get sorted set range', async () => {
      const key = 'test:zset';
      const members = ['member1', 'member2'];

      mockRedis.zrange.mockResolvedValue(members);

      const result = await service.zrange(key, 0, -1);

      expect(mockRedis.zrange).toHaveBeenCalledWith(key, 0, -1);
      expect(result).toEqual(members);
    });

    it('should get sorted set range with scores', async () => {
      const key = 'test:zset';
      const result = ['member1', '100', 'member2', '200'];

      mockRedis.zrange.mockResolvedValue(result);

      const members = await service.zrange(key, 0, -1, true);

      expect(mockRedis.zrange).toHaveBeenCalledWith(key, 0, -1, 'WITHSCORES');
      expect(members).toEqual([
        { member: 'member1', score: 100 },
        { member: 'member2', score: 200 },
      ]);
    });

    it('should get member score', async () => {
      const key = 'test:zset';
      const member = 'member1';

      mockRedis.zscore.mockResolvedValue('100');

      const result = await service.zscore(key, member);

      expect(mockRedis.zscore).toHaveBeenCalledWith(key, member);
      expect(result).toBe(100);
    });

    it('should remove from sorted set', async () => {
      const key = 'test:zset';
      const members = ['member1', 'member2'];

      mockRedis.zrem.mockResolvedValue(2);

      const result = await service.zrem(key, ...members);

      expect(mockRedis.zrem).toHaveBeenCalledWith(key, ...members);
      expect(result).toBe(2);
    });
  });

  describe('Utility Methods', () => {
    it('should build key with parameters', () => {
      const pattern = 'user:{userId}:profile';
      const params = { userId: '123' };

      const result = service.buildKey(pattern, params);

      expect(result).toBe('user:123:profile');
    });

    it('should ping Redis', async () => {
      mockRedis.ping.mockResolvedValue('PONG');

      const result = await service.ping();

      expect(mockRedis.ping).toHaveBeenCalled();
      expect(result).toBe(true);
    });

    it('should handle ping error', async () => {
      mockRedis.ping.mockRejectedValue(new Error('Connection failed'));

      const result = await service.ping();

      expect(result).toBe(false);
    });

    it('should get Redis info', async () => {
      const info = 'redis_version:7.0.0\r\nused_memory:1000000\r\n';
      mockRedis.info.mockResolvedValue(info);

      const result = await service.getInfo();

      expect(mockRedis.info).toHaveBeenCalled();
      expect(result).toEqual({
        redis_version: '7.0.0',
        used_memory: 1000000,
      });
    });

    it('should get Redis client', () => {
      const client = service.getClient();
      expect(client).toBe(mockRedis);
    });

    it('should get subscriber client', () => {
      const subscriber = service.getSubscriber();
      expect(subscriber).toBe(mockRedis);
    });
  });

  describe('Error Handling', () => {
    it('should handle set error', async () => {
      const key = 'test:key';
      const value = { test: 'data' };
      const error = new Error('Redis error');

      mockRedis.set.mockRejectedValue(error);

      await expect(service.set(key, value)).rejects.toThrow('Redis error');
    });

    it('should handle get error', async () => {
      const key = 'test:key';
      const error = new Error('Redis error');

      mockRedis.get.mockRejectedValue(error);

      await expect(service.get(key)).rejects.toThrow('Redis error');
    });
  });
});
