import { Test, TestingModule } from '@nestjs/testing';
import { RedisLockService } from '../redis-lock.service';
import { RedisService } from '../redis.service';
import { MockRedisClient, SilentLogger, TestAssertions } from './test-utils';

describe('RedisLockService', () => {
  let service: RedisLockService;
  let redisService: RedisService;
  let mockClient: MockRedisClient;

  beforeEach(async () => {
    mockClient = new MockRedisClient();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RedisLockService,
        {
          provide: RedisService,
          useValue: {
            getClient: () => mockClient,
            eval: jest.fn(),
            set: jest.fn(),
            get: jest.fn(),
            del: jest.fn(),
            exists: jest.fn(),
            expire: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<RedisLockService>(RedisLockService);
    redisService = module.get<RedisService>(RedisService);
    
    // 使用静默日志记录器
    (service as any).logger = new SilentLogger();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('基础锁操作', () => {
    it('应该能够获取锁', async () => {
      const lockKey = 'test:lock';
      const options = { ttl: 30, maxRetries: 3, retryDelay: 100 };

      jest.spyOn(redisService, 'eval').mockResolvedValue(1); // 成功获取锁

      const lockId = await service.acquireLock(lockKey, options);

      expect(lockId).toBeDefined();
      expect(lockId).toMatch(/^lock_\d+_[a-z0-9]+$/);
      expect(redisService.eval).toHaveBeenCalled();
    });

    it('应该在锁被占用时返回 null', async () => {
      const lockKey = 'test:lock:occupied';
      const options = { ttl: 30, maxRetries: 1, retryDelay: 10 };

      jest.spyOn(redisService, 'eval').mockResolvedValue(0); // 锁被占用

      const lockId = await service.acquireLock(lockKey, options);

      expect(lockId).toBeNull();
    });

    it('应该能够释放锁', async () => {
      const lockKey = 'test:lock:release';
      const lockId = 'test-lock-id';

      jest.spyOn(redisService, 'eval').mockResolvedValue(1); // 成功释放

      const released = await service.releaseLock(lockKey, lockId);

      expect(released).toBe(true);
      expect(redisService.eval).toHaveBeenCalledWith(
        expect.stringContaining('redis.call("get", KEYS[1])'),
        1,
        lockKey,
        lockId
      );
    });

    it('应该在释放不存在的锁时返回 false', async () => {
      const lockKey = 'test:lock:nonexistent';
      const lockId = 'invalid-lock-id';

      jest.spyOn(redisService, 'eval').mockResolvedValue(0); // 释放失败

      const released = await service.releaseLock(lockKey, lockId);

      expect(released).toBe(false);
    });

    it('应该能够检查锁是否存在', async () => {
      const lockKey = 'test:lock:exists';

      jest.spyOn(redisService, 'exists').mockResolvedValue(true);

      const exists = await service.isLocked(lockKey);

      expect(exists).toBe(true);
      expect(redisService.exists).toHaveBeenCalledWith(lockKey);
    });
  });

  describe('重试机制', () => {
    it('应该在指定次数内重试获取锁', async () => {
      const lockKey = 'test:lock:retry';
      const options = { ttl: 30, maxRetries: 3, retryDelay: 10 };

      jest.spyOn(redisService, 'eval')
        .mockResolvedValueOnce(0) // 第一次失败
        .mockResolvedValueOnce(0) // 第二次失败
        .mockResolvedValueOnce(1); // 第三次成功

      const startTime = Date.now();
      const lockId = await service.acquireLock(lockKey, options);
      const duration = Date.now() - startTime;

      expect(lockId).toBeDefined();
      expect(redisService.eval).toHaveBeenCalledTimes(3);
      expect(duration).toBeGreaterThanOrEqual(20); // 至少等待了两次重试延迟
    });

    it('应该在达到最大重试次数后放弃', async () => {
      const lockKey = 'test:lock:max-retries';
      const options = { ttl: 30, maxRetries: 2, retryDelay: 10 };

      jest.spyOn(redisService, 'eval').mockResolvedValue(0); // 总是失败

      const lockId = await service.acquireLock(lockKey, options);

      expect(lockId).toBeNull();
      expect(redisService.eval).toHaveBeenCalledTimes(3); // 初始尝试 + 2次重试
    });
  });

  describe('可重入锁', () => {
    it('应该能够获取可重入锁', async () => {
      const lockKey = 'test:reentrant:lock';
      const options = { ttl: 30, reentrant: true };

      jest.spyOn(redisService, 'eval').mockResolvedValue(1);

      const lockId = await service.acquireReentrantLock(lockKey, options);

      expect(lockId).toBeDefined();
      expect(redisService.eval).toHaveBeenCalled();
    });

    it('应该允许同一标识符多次获取可重入锁', async () => {
      const lockKey = 'test:reentrant:multiple';
      const options = { ttl: 30, reentrant: true };

      jest.spyOn(redisService, 'eval').mockResolvedValue(1);

      const lockId1 = await service.acquireReentrantLock(lockKey, options);
      const lockId2 = await service.acquireReentrantLock(lockKey, options);

      expect(lockId1).toBeDefined();
      expect(lockId2).toBeDefined();
      expect(lockId1).toBe(lockId2); // 应该返回相同的锁ID
    });

    it('应该能够释放可重入锁', async () => {
      const lockKey = 'test:reentrant:release';
      const lockId = 'reentrant-lock-id';

      jest.spyOn(redisService, 'eval').mockResolvedValue(1);

      const released = await service.releaseReentrantLock(lockKey, lockId);

      expect(released).toBe(true);
      expect(redisService.eval).toHaveBeenCalled();
    });
  });

  describe('withLock 方法', () => {
    it('应该在锁保护下执行函数', async () => {
      const lockKey = 'test:with-lock';
      const expectedResult = 'function result';
      const testFunction = jest.fn().mockResolvedValue(expectedResult);

      jest.spyOn(redisService, 'eval')
        .mockResolvedValueOnce(1) // 获取锁成功
        .mockResolvedValueOnce(1); // 释放锁成功

      const result = await service.withLock(lockKey, testFunction, { ttl: 30 });

      expect(result).toBe(expectedResult);
      expect(testFunction).toHaveBeenCalled();
      expect(redisService.eval).toHaveBeenCalledTimes(2); // 获取锁 + 释放锁
    });

    it('应该在函数执行后释放锁', async () => {
      const lockKey = 'test:with-lock:cleanup';
      const testFunction = jest.fn().mockResolvedValue('result');

      jest.spyOn(redisService, 'eval')
        .mockResolvedValueOnce(1) // 获取锁成功
        .mockResolvedValueOnce(1); // 释放锁成功

      await service.withLock(lockKey, testFunction, { ttl: 30 });

      // 验证释放锁被调用
      expect(redisService.eval).toHaveBeenCalledTimes(2);
    });

    it('应该在函数抛出异常时仍然释放锁', async () => {
      const lockKey = 'test:with-lock:error';
      const testError = new Error('Test error');
      const testFunction = jest.fn().mockRejectedValue(testError);

      jest.spyOn(redisService, 'eval')
        .mockResolvedValueOnce(1) // 获取锁成功
        .mockResolvedValueOnce(1); // 释放锁成功

      await expect(service.withLock(lockKey, testFunction, { ttl: 30 }))
        .rejects.toThrow('Test error');

      // 验证释放锁仍然被调用
      expect(redisService.eval).toHaveBeenCalledTimes(2);
    });

    it('应该在无法获取锁时抛出错误', async () => {
      const lockKey = 'test:with-lock:no-lock';
      const testFunction = jest.fn();

      jest.spyOn(redisService, 'eval').mockResolvedValue(0); // 获取锁失败

      await expect(service.withLock(lockKey, testFunction, { ttl: 30, maxRetries: 1 }))
        .rejects.toThrow('Failed to acquire lock');

      expect(testFunction).not.toHaveBeenCalled();
    });
  });

  describe('业务专用锁', () => {
    it('应该能够获取转会锁', async () => {
      const playerId = 'player123';

      jest.spyOn(redisService, 'eval').mockResolvedValue(1);

      const lockId = await service.acquireTransferLock(playerId);

      expect(lockId).toBeDefined();
      expect(redisService.eval).toHaveBeenCalledWith(
        expect.any(String),
        1,
        'transfer:player:player123',
        expect.any(String),
        300 // 默认5分钟TTL
      );
    });

    it('应该能够获取俱乐部锁', async () => {
      const clubId = 'club456';
      const operation = 'training';

      jest.spyOn(redisService, 'eval').mockResolvedValue(1);

      const lockId = await service.acquireClubLock(clubId, operation);

      expect(lockId).toBeDefined();
      expect(redisService.eval).toHaveBeenCalledWith(
        expect.any(String),
        1,
        'club:club456:training',
        expect.any(String),
        60 // 默认1分钟TTL
      );
    });

    it('应该能够获取比赛锁', async () => {
      const matchId = 'match789';

      jest.spyOn(redisService, 'eval').mockResolvedValue(1);

      const lockId = await service.acquireMatchLock(matchId);

      expect(lockId).toBeDefined();
      expect(redisService.eval).toHaveBeenCalledWith(
        expect.any(String),
        1,
        'match:simulation:match789',
        expect.any(String),
        1800 // 默认30分钟TTL
      );
    });

    it('应该能够获取排行榜锁', async () => {
      const type = 'global';

      jest.spyOn(redisService, 'eval').mockResolvedValue(1);

      const lockId = await service.acquireLeaderboardLock(type);

      expect(lockId).toBeDefined();
      expect(redisService.eval).toHaveBeenCalledWith(
        expect.any(String),
        1,
        'leaderboard:update:global',
        expect.any(String),
        120 // 默认2分钟TTL
      );
    });
  });

  describe('锁统计', () => {
    it('应该返回正确的锁统计信息', async () => {
      jest.spyOn(redisService, 'eval').mockResolvedValue(1);

      // 获取一些锁
      await service.acquireLock('lock1', { ttl: 30 });
      await service.acquireLock('lock2', { ttl: 30 });
      await service.acquireLock('lock3', { ttl: 30 }); // 这个会失败

      jest.spyOn(redisService, 'eval').mockResolvedValue(0);
      await service.acquireLock('lock4', { ttl: 30, maxRetries: 1 });

      const stats = service.getStats();

      expect(stats.totalAcquired).toBe(3);
      expect(stats.totalFailed).toBe(1);
      expect(stats.currentlyHeld).toBe(3);
      expect(stats.averageHoldTime).toBeGreaterThan(0);
    });

    it('应该能够重置统计信息', async () => {
      jest.spyOn(redisService, 'eval').mockResolvedValue(1);

      await service.acquireLock('test-lock', { ttl: 30 });

      let stats = service.getStats();
      expect(stats.totalAcquired).toBe(1);

      service.resetStats();

      stats = service.getStats();
      expect(stats.totalAcquired).toBe(0);
    });
  });

  describe('错误处理', () => {
    it('应该处理 Redis 错误', async () => {
      const lockKey = 'test:lock:error';

      jest.spyOn(redisService, 'eval').mockRejectedValue(new Error('Redis error'));

      await expect(service.acquireLock(lockKey, { ttl: 30 }))
        .rejects.toThrow('Redis error');
    });

    it('应该处理无效的锁选项', async () => {
      const lockKey = 'test:lock:invalid';

      await expect(service.acquireLock(lockKey, { ttl: -1 }))
        .rejects.toThrow();
    });

    it('应该处理空的锁键', async () => {
      await expect(service.acquireLock('', { ttl: 30 }))
        .rejects.toThrow();
    });
  });

  describe('并发测试', () => {
    it('应该正确处理并发锁请求', async () => {
      const lockKey = 'test:concurrent:lock';
      const concurrentRequests = 10;

      let successCount = 0;
      jest.spyOn(redisService, 'eval').mockImplementation(() => {
        successCount++;
        return Promise.resolve(successCount === 1 ? 1 : 0); // 只有第一个成功
      });

      const promises = Array.from({ length: concurrentRequests }, () =>
        service.acquireLock(lockKey, { ttl: 30, maxRetries: 1 })
      );

      const results = await Promise.all(promises);
      const successfulLocks = results.filter(result => result !== null);

      expect(successfulLocks).toHaveLength(1); // 只有一个应该成功
    });
  });

  describe('私有方法测试', () => {
    it('应该生成唯一的锁标识符', () => {
      const id1 = (service as any).generateLockId();
      const id2 = (service as any).generateLockId();

      expect(id1).toMatch(/^lock_\d+_[a-z0-9]+$/);
      expect(id2).toMatch(/^lock_\d+_[a-z0-9]+$/);
      expect(id1).not.toBe(id2);
    });

    it('应该验证锁选项', () => {
      expect(() => (service as any).validateLockOptions({ ttl: 30 })).not.toThrow();
      expect(() => (service as any).validateLockOptions({ ttl: -1 })).toThrow();
      expect(() => (service as any).validateLockOptions({ maxRetries: -1 })).toThrow();
    });

    it('应该正确计算重试延迟', () => {
      const baseDelay = 100;
      const attempt = 2;

      const delay = (service as any).calculateRetryDelay(baseDelay, attempt);

      expect(delay).toBeGreaterThanOrEqual(baseDelay * attempt);
    });
  });
});
