#!/bin/bash

# Redis 服务测试运行脚本
# 提供多种测试运行选项和报告生成

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../../.." && pwd)"

# 默认配置
TEST_PATTERN="*.spec.ts"
COVERAGE=false
WATCH=false
VERBOSE=false
BAIL=false
UPDATE_SNAPSHOTS=false
SPECIFIC_TEST=""
PARALLEL=true
PERFORMANCE_MONITORING=false

# 帮助信息
show_help() {
    echo -e "${BLUE}Redis 服务测试运行器${NC}"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -c, --coverage          生成覆盖率报告"
    echo "  -w, --watch             监视模式"
    echo "  -v, --verbose           详细输出"
    echo "  -b, --bail              遇到错误时停止"
    echo "  -u, --update-snapshots  更新快照"
    echo "  -t, --test <pattern>    运行特定测试"
    echo "  -s, --sequential        顺序运行测试"
    echo "  -p, --performance       启用性能监控"
    echo "  --unit                  只运行单元测试"
    echo "  --integration           只运行集成测试"
    echo "  --e2e                   只运行端到端测试"
    echo ""
    echo "示例:"
    echo "  $0 -c                   # 运行所有测试并生成覆盖率报告"
    echo "  $0 -t redis.service     # 运行 Redis 服务相关测试"
    echo "  $0 -w -v                # 监视模式，详细输出"
    echo "  $0 --unit -c            # 只运行单元测试并生成覆盖率"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -c|--coverage)
            COVERAGE=true
            shift
            ;;
        -w|--watch)
            WATCH=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -b|--bail)
            BAIL=true
            shift
            ;;
        -u|--update-snapshots)
            UPDATE_SNAPSHOTS=true
            shift
            ;;
        -t|--test)
            SPECIFIC_TEST="$2"
            shift 2
            ;;
        -s|--sequential)
            PARALLEL=false
            shift
            ;;
        -p|--performance)
            PERFORMANCE_MONITORING=true
            shift
            ;;
        --unit)
            TEST_PATTERN="*.spec.ts"
            shift
            ;;
        --integration)
            TEST_PATTERN="*.integration.spec.ts"
            shift
            ;;
        --e2e)
            TEST_PATTERN="*.e2e.spec.ts"
            shift
            ;;
        *)
            echo -e "${RED}未知选项: $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

# 检查依赖
check_dependencies() {
    echo -e "${BLUE}检查依赖...${NC}"
    
    if ! command -v node &> /dev/null; then
        echo -e "${RED}错误: Node.js 未安装${NC}"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}错误: npm 未安装${NC}"
        exit 1
    fi
    
    if [ ! -f "$PROJECT_ROOT/package.json" ]; then
        echo -e "${RED}错误: 未找到 package.json${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}依赖检查通过${NC}"
}

# 设置环境变量
setup_environment() {
    echo -e "${BLUE}设置测试环境...${NC}"
    
    export NODE_ENV=test
    export REDIS_HOST=localhost
    export REDIS_PORT=6379
    export REDIS_DB=15
    
    if [ "$VERBOSE" = true ]; then
        export ENABLE_TEST_LOGS=true
    fi
    
    if [ "$PERFORMANCE_MONITORING" = true ]; then
        export ENABLE_PERFORMANCE_MONITORING=true
    fi
    
    echo -e "${GREEN}环境设置完成${NC}"
}

# 清理旧的测试结果
cleanup() {
    echo -e "${BLUE}清理旧的测试结果...${NC}"
    
    if [ -d "$SCRIPT_DIR/coverage" ]; then
        rm -rf "$SCRIPT_DIR/coverage"
    fi
    
    if [ -d "$SCRIPT_DIR/test-results" ]; then
        rm -rf "$SCRIPT_DIR/test-results"
    fi
    
    echo -e "${GREEN}清理完成${NC}"
}

# 构建 Jest 命令
build_jest_command() {
    local cmd="npx jest --config=$SCRIPT_DIR/jest.config.js"
    
    if [ "$COVERAGE" = true ]; then
        cmd="$cmd --coverage"
    fi
    
    if [ "$WATCH" = true ]; then
        cmd="$cmd --watch"
    fi
    
    if [ "$VERBOSE" = true ]; then
        cmd="$cmd --verbose"
    fi
    
    if [ "$BAIL" = true ]; then
        cmd="$cmd --bail"
    fi
    
    if [ "$UPDATE_SNAPSHOTS" = true ]; then
        cmd="$cmd --updateSnapshot"
    fi
    
    if [ "$PARALLEL" = false ]; then
        cmd="$cmd --runInBand"
    fi
    
    if [ -n "$SPECIFIC_TEST" ]; then
        cmd="$cmd --testNamePattern=\"$SPECIFIC_TEST\""
    fi
    
    cmd="$cmd --testMatch=\"**/$TEST_PATTERN\""
    
    echo "$cmd"
}

# 运行测试
run_tests() {
    echo -e "${BLUE}运行测试...${NC}"
    
    local jest_cmd=$(build_jest_command)
    
    echo -e "${YELLOW}执行命令: $jest_cmd${NC}"
    echo ""
    
    cd "$PROJECT_ROOT"
    
    if eval "$jest_cmd"; then
        echo ""
        echo -e "${GREEN}✅ 测试运行成功${NC}"
        return 0
    else
        echo ""
        echo -e "${RED}❌ 测试运行失败${NC}"
        return 1
    fi
}

# 生成测试报告
generate_report() {
    if [ "$COVERAGE" = true ] && [ -d "$SCRIPT_DIR/coverage" ]; then
        echo -e "${BLUE}生成测试报告...${NC}"
        
        # 生成 HTML 报告
        if [ -f "$SCRIPT_DIR/coverage/lcov.info" ]; then
            echo -e "${GREEN}覆盖率报告已生成: $SCRIPT_DIR/coverage/index.html${NC}"
        fi
        
        # 显示覆盖率摘要
        if [ -f "$SCRIPT_DIR/coverage/coverage-summary.json" ]; then
            echo -e "${BLUE}覆盖率摘要:${NC}"
            node -e "
                const summary = require('$SCRIPT_DIR/coverage/coverage-summary.json');
                const total = summary.total;
                console.log(\`  语句: \${total.statements.pct}%\`);
                console.log(\`  分支: \${total.branches.pct}%\`);
                console.log(\`  函数: \${total.functions.pct}%\`);
                console.log(\`  行数: \${total.lines.pct}%\`);
            "
        fi
    fi
}

# 主函数
main() {
    echo -e "${BLUE}🧪 Redis 服务测试运行器${NC}"
    echo ""
    
    check_dependencies
    setup_environment
    
    if [ "$WATCH" = false ]; then
        cleanup
    fi
    
    if run_tests; then
        generate_report
        echo ""
        echo -e "${GREEN}🎉 测试完成！${NC}"
        exit 0
    else
        echo ""
        echo -e "${RED}💥 测试失败！${NC}"
        exit 1
    fi
}

# 信号处理
trap 'echo -e "\n${YELLOW}测试被中断${NC}"; exit 130' INT TERM

# 运行主函数
main "$@"
