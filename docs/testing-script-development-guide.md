# 测试脚本开发指南

## 核心原则

### 1. 基于真实业务逻辑，杜绝凭空猜测

**❌ 错误做法：**
- 凭空猜测API接口的数据格式
- 假设返回数据结构而不验证
- 基于经验编写测试而不查看实际代码

**✅ 正确做法：**
- 必须先使用 `codebase-retrieval` 工具分析相关业务代码
- 查看实际的Controller、Service、DTO定义
- 理解数据流转和业务逻辑
- 基于真实接口文档编写测试

### 2. 深度代码分析流程

#### 步骤1：业务逻辑分析
```bash
# 使用codebase-retrieval分析目标功能
codebase-retrieval("查找用户登录相关的Controller和Service实现")
codebase-retrieval("分析Token刷新的完整业务流程和数据格式")
```

#### 步骤2：接口定义分析
- 查看Controller中的路由定义
- 分析DTO的字段要求和验证规则
- 理解返回数据的结构和格式
- 确认错误处理机制

#### 步骤3：数据流分析
- 追踪数据在微服务间的传递
- 理解数据转换和映射逻辑
- 确认缓存和持久化机制

## 测试脚本编写规范

### 1. 文件结构标准

```javascript
// 1. 导入和配置
const axios = require('axios');
const io = require('socket.io-client');

// 2. 配置常量（基于真实环境配置）
const CONFIG = {
  GATEWAY_HTTP_URL: 'http://127.0.0.1:3000',
  GATEWAY_WS_URL: 'http://127.0.0.1:3000',
  // 基于实际业务需求的测试数据
  TEST_USER: {
    username: `test_user_${Date.now()}`,
    email: `test_${Date.now()}@example.com`,
    password: 'SecureP@ssw0rd!', // 符合密码策略
  }
};

// 3. 测试类定义
class BusinessLogicTester {
  constructor() {
    this.testResults = [];
    this.authTokens = {};
  }
}
```

### 2. 数据处理规范

#### 响应数据解析
```javascript
// ❌ 错误：凭空猜测数据结构
const token = response.data.token;

// ✅ 正确：基于实际业务逻辑解析
console.log('✅ 完整响应:', JSON.stringify(response.data, null, 2));

// 基于代码分析确定的数据结构
const responseData = response.data;
if (responseData.success && responseData.data) {
  const tokenData = responseData.data.data || responseData.data;
  this.accessToken = tokenData.tokens?.accessToken || tokenData.accessToken;
  
  // 安全地获取用户ID（基于实际Entity定义）
  if (tokenData.user && tokenData.user._id) {
    this.userId = typeof tokenData.user._id === 'object' 
      ? tokenData.user._id.toString() 
      : tokenData.user._id;
  }
}
```

#### 错误处理标准
```javascript
// 基于实际业务异常类型处理
try {
  const response = await axios.post(url, data);
  this.recordTest('功能测试', true, `成功: ${response.data.message}`);
} catch (error) {
  // 详细错误分析
  const errorInfo = {
    status: error.response?.status,
    statusText: error.response?.statusText,
    data: error.response?.data,
    message: error.message
  };
  
  console.log('❌ 错误详情:', JSON.stringify(errorInfo, null, 2));
  
  // 基于业务逻辑判断是否为预期错误
  const isExpectedError = this.isExpectedBusinessError(error);
  this.recordTest('功能测试', isExpectedError, 
    `错误处理: ${isExpectedError ? '符合预期' : '异常错误'}`);
}
```

### 3. 微服务通信测试规范

#### WebSocket调用格式
```javascript
// 基于实际MessagePattern定义
const command = 'auth.verifyToken'; // 不是 'verifyToken'
const payload = { token: this.accessToken };

// 超时配置基于实际业务需求
const timeout = 10000; // 10秒，不是随意设置
```

#### HTTP调用格式
```javascript
// 基于实际路由配置
const url = `${GATEWAY_HTTP_URL}/user/me`; // 不是 '/api/user/profile'
const headers = {
  'Authorization': `Bearer ${this.accessToken}`,
  'Content-Type': 'application/json'
};
```

## 问题定位方法论

### 1. 系统性错误分析

#### 日志分析流程
1. **收集完整日志** - Gateway、Auth、其他微服务
2. **时间序列分析** - 按时间顺序追踪请求流
3. **错误关联分析** - 找出错误的根本原因
4. **业务逻辑验证** - 确认是否符合设计预期

#### 错误分类标准
- **配置错误** - 环境变量、连接配置等
- **业务逻辑错误** - 数据验证、状态管理等
- **架构问题** - 微服务通信、数据一致性等
- **测试脚本错误** - 错误的假设和实现

### 2. 深度问题定位

#### 数据一致性检查
```javascript
// 验证数据在整个流程中的一致性
console.log('🔍 数据一致性检查:');
console.log('登录返回的sessionId:', loginResponse.sessionId);
console.log('Token中的sessionId:', this.parseJWT(token).sessionId);
console.log('数据库中的sessionId:', await this.querySession(sessionId));
```

#### 业务状态验证
```javascript
// 验证业务对象的状态
const userStatus = await this.getUserStatus(userId);
const sessionStatus = await this.getSessionStatus(sessionId);
const tokenStatus = await this.getTokenStatus(token);

console.log('🔍 业务状态验证:', {
  user: userStatus,
  session: sessionStatus,
  token: tokenStatus
});
```

## 测试覆盖度要求

### 1. 功能测试覆盖
- ✅ 正常业务流程
- ✅ 边界条件测试
- ✅ 异常情况处理
- ✅ 并发场景测试

### 2. 数据验证覆盖
- ✅ 输入数据验证
- ✅ 输出数据格式
- ✅ 数据转换正确性
- ✅ 数据持久化验证

### 3. 集成测试覆盖
- ✅ 微服务间通信
- ✅ 数据库操作
- ✅ 缓存机制
- ✅ 外部依赖

## 经验教训总结

### 本次测试中发现的问题

1. **会话ID不匹配问题**
   - 原因：Token使用MongoDB ObjectId，但验证使用自定义sessionId
   - 教训：必须理解数据模型的完整定义
   - 解决：统一使用session.sessionId而不是session.id

2. **数据结构假设错误**
   - 原因：凭空猜测API返回格式
   - 教训：必须查看实际Controller和DTO定义
   - 解决：基于真实代码分析编写测试

3. **微服务通信格式错误**
   - 原因：不了解MessagePattern的命名规范[microservice-quick-reference.md](microservice-quick-reference.md)
   - 教训：必须查看实际的微服务接口定义
   - 解决：使用正确的service.action格式

### 最佳实践

1. **代码优先原则** - 先分析代码，再编写测试
2. **日志驱动调试** - 通过日志理解业务流程
3. **渐进式测试** - 从简单功能开始，逐步复杂化
4. **数据一致性验证** - 确保数据在整个流程中保持一致
5. **错误分类处理** - 区分业务错误和技术错误

## 工具使用规范

### 必须使用的分析工具
1. `codebase-retrieval` - 业务逻辑分析
2. `view` - 查看具体文件内容
3. `web-search` - 查找技术文档
4. `read-process` - 分析服务日志

### 禁止的做法
1. ❌ 凭空猜测API格式
2. ❌ 复制其他项目的测试代码
3. ❌ 忽略错误日志信息
4. ❌ 不验证业务逻辑就编写测试

---

**记住：测试脚本的质量直接影响问题定位的效率。投入时间深入分析业务逻辑，比花时间调试错误的测试脚本更有价值。**
