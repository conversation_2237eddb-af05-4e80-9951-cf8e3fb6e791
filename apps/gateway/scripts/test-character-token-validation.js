const axios = require('axios');

/**
 * 角色Token验证测试脚本
 * 
 * 功能：
 * 1. 测试角色Token验证接口
 * 2. 验证Auth微服务调用
 * 3. 测试各种Token场景
 */

// 配置
const GATEWAY_HTTP_URL = 'http://127.0.0.1:3000';

class CharacterTokenValidationTester {
  constructor() {
    this.testResults = [];
    this.accessToken = null;
    this.characterToken = null;
  }

  /**
   * 记录测试结果
   */
  recordTest(testName, success, details = '') {
    const result = {
      testName,
      success,
      details,
      timestamp: new Date().toISOString(),
    };
    this.testResults.push(result);
    
    const status = success ? '✅' : '❌';
    console.log(`  ${status} ${testName}: ${details}`);
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始角色Token验证测试');
    console.log('=' .repeat(60));

    try {
      // 阶段1：准备测试数据
      await this.prepareTestData();
      
      // 阶段2：测试有效Token验证
      await this.testValidTokenValidation();
      
      // 阶段3：测试无效Token验证
      await this.testInvalidTokenValidation();
      
      // 阶段4：测试边界情况
      await this.testEdgeCases();

      // 输出测试结果
      this.printTestResults();
      
    } catch (error) {
      console.error('❌ 测试执行失败:', error.message);
    }
  }

  /**
   * 准备测试数据
   */
  async prepareTestData() {
    console.log('\n📋 阶段1：准备测试数据');

    try {
      // 1. 用户登录获取账号Token - 使用固定的测试用户
      const testUsername = 'test_proxy_user_1753933959063'; // 使用之前测试中的用户

      try {
        const loginResponse = await axios.post(`${GATEWAY_HTTP_URL}/user/login`, {
          username: testUsername,
          password: 'SecureP@ssw0rd!',
        });

        if (loginResponse.data.success) {
          this.accessToken = loginResponse.data.accessToken;
          this.recordTest('用户登录', true, '获取账号Token成功');
        } else {
          throw new Error('登录失败');
        }
      } catch (loginError) {
        // 如果固定用户登录失败，尝试注册新用户
        console.log('  ⚠️ 固定用户登录失败，尝试注册新用户...');
        const newUsername = 'test_user_' + Date.now();

        try {
          await axios.post(`${GATEWAY_HTTP_URL}/user/register`, {
            username: newUsername,
            password: 'SecureP@ssw0rd!',
            email: `${newUsername}@test.com`,
          });

          const retryLogin = await axios.post(`${GATEWAY_HTTP_URL}/user/login`, {
            username: newUsername,
            password: 'SecureP@ssw0rd!',
          });

          this.accessToken = retryLogin.data.accessToken;
          this.recordTest('用户注册和登录', true, '新用户注册登录成功');
        } catch (registerError) {
          throw new Error(`注册和登录都失败: ${registerError.message}`);
        }
      }

      // 2. 创建角色获取角色Token
      await this.createCharacterAndGetToken();

    } catch (error) {
      this.recordTest('准备测试数据', false, `失败: ${error.message}`);
      throw error;
    }
  }



  /**
   * 创建角色并获取角色Token
   */
  async createCharacterAndGetToken() {
    try {
      // 创建角色
      const createResponse = await axios.post(`${GATEWAY_HTTP_URL}/character/create`, {
        serverId: 'server_001',
        name: 'TestCharacter_' + Date.now(),
        faceIcon: 1,
      }, {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
        },
      });

      if (createResponse.data.success) {
        const character = createResponse.data.character;
        this.recordTest('创建角色', true, `角色ID: ${character.characterId}`);

        // 角色登录获取角色Token
        const loginResponse = await axios.post(`${GATEWAY_HTTP_URL}/character/login`, {
          serverId: 'server_001',
          characterId: character.characterId,
        }, {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
          },
        });

        if (loginResponse.data.success) {
          this.characterToken = loginResponse.data.characterToken;
          this.recordTest('角色登录', true, '获取角色Token成功');
        } else {
          throw new Error('角色登录失败');
        }
      } else {
        throw new Error('创建角色失败');
      }
    } catch (error) {
      this.recordTest('创建角色和获取Token', false, `失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 测试有效Token验证
   */
  async testValidTokenValidation() {
    console.log('\n🔍 阶段2：测试有效Token验证');

    if (!this.characterToken) {
      this.recordTest('有效Token验证', false, '缺少角色Token');
      return;
    }

    try {
      const response = await axios.post(`${GATEWAY_HTTP_URL}/character/validate-token`, {
        characterToken: this.characterToken,
      });

      const result = response.data;
      
      if (result.valid) {
        this.recordTest('有效Token验证', true, `验证成功，角色ID: ${result.payload?.characterId}`);
        
        // 验证返回的数据结构
        if (result.payload) {
          const requiredFields = ['userId', 'characterId', 'serverId', 'characterName', 'scope', 'type'];
          const missingFields = requiredFields.filter(field => !result.payload[field]);
          
          if (missingFields.length === 0) {
            this.recordTest('Token载荷结构验证', true, '所有必需字段都存在');
          } else {
            this.recordTest('Token载荷结构验证', false, `缺少字段: ${missingFields.join(', ')}`);
          }
        }

        // 验证角色信息
        if (result.character) {
          this.recordTest('角色信息返回', true, `角色名: ${result.character.name}`);
        }

        // 验证区服信息
        if (result.server) {
          this.recordTest('区服信息返回', true, `区服ID: ${result.server.id}`);
        }
      } else {
        this.recordTest('有效Token验证', false, `验证失败: ${result.message}`);
      }
    } catch (error) {
      this.recordTest('有效Token验证', false, `请求失败: ${error.message}`);
    }
  }

  /**
   * 测试无效Token验证
   */
  async testInvalidTokenValidation() {
    console.log('\n❌ 阶段3：测试无效Token验证');

    const invalidTokens = [
      { name: '过期Token', token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c' },
      { name: '格式错误Token', token: 'invalid.token.format' },
      { name: '空Token', token: '' },
      { name: '非JWT格式', token: 'not-a-jwt-token' },
    ];

    for (const testCase of invalidTokens) {
      try {
        const response = await axios.post(`${GATEWAY_HTTP_URL}/character/validate-token`, {
          characterToken: testCase.token,
        });

        const result = response.data;
        
        if (!result.valid) {
          this.recordTest(`无效Token验证-${testCase.name}`, true, `正确识别为无效: ${result.message}`);
        } else {
          this.recordTest(`无效Token验证-${testCase.name}`, false, '错误地验证为有效');
        }
      } catch (error) {
        // 对于某些无效Token，可能会直接返回400错误，这也是正确的
        if (error.response && error.response.status === 400) {
          this.recordTest(`无效Token验证-${testCase.name}`, true, '正确返回400错误');
        } else {
          this.recordTest(`无效Token验证-${testCase.name}`, false, `意外错误: ${error.message}`);
        }
      }
    }
  }

  /**
   * 测试边界情况
   */
  async testEdgeCases() {
    console.log('\n🔬 阶段4：测试边界情况');

    // 测试缺少参数
    try {
      const response = await axios.post(`${GATEWAY_HTTP_URL}/character/validate-token`, {});
      
      const result = response.data;
      if (!result.valid) {
        this.recordTest('缺少Token参数', true, '正确处理缺少参数的情况');
      } else {
        this.recordTest('缺少Token参数', false, '未正确处理缺少参数的情况');
      }
    } catch (error) {
      if (error.response && error.response.status === 400) {
        this.recordTest('缺少Token参数', true, '正确返回400错误');
      } else {
        this.recordTest('缺少Token参数', false, `意外错误: ${error.message}`);
      }
    }

    // 测试null值
    try {
      const response = await axios.post(`${GATEWAY_HTTP_URL}/character/validate-token`, {
        characterToken: null,
      });
      
      const result = response.data;
      if (!result.valid) {
        this.recordTest('null Token', true, '正确处理null值');
      } else {
        this.recordTest('null Token', false, '未正确处理null值');
      }
    } catch (error) {
      if (error.response && error.response.status === 400) {
        this.recordTest('null Token', true, '正确返回400错误');
      } else {
        this.recordTest('null Token', false, `意外错误: ${error.message}`);
      }
    }
  }

  /**
   * 输出测试结果
   */
  printTestResults() {
    console.log('\n📊 测试结果汇总');
    console.log('=' .repeat(60));

    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;

    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${passedTests} ✅`);
    console.log(`失败: ${failedTests} ❌`);
    console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(2)}%`);

    if (failedTests > 0) {
      console.log('\n❌ 失败的测试:');
      this.testResults
        .filter(r => !r.success)
        .forEach(r => console.log(`  - ${r.testName}: ${r.details}`));
    }

    console.log('\n🎯 角色Token验证测试完成');
  }
}

// 运行测试
if (require.main === module) {
  const tester = new CharacterTokenValidationTester();
  tester.runAllTests().catch(console.error);
}

module.exports = CharacterTokenValidationTester;
