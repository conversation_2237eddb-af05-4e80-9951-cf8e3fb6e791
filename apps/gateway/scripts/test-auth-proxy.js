/**
 * Gateway认证代理功能测试脚本
 * 
 * 测试Gateway是否能正确代理所有认证请求到Auth服务
 * 严格按照"皮蛋"方案验证代理功能的完整性
 */

const WebSocket = require('ws');
const axios = require('axios');

// 配置
const GATEWAY_HTTP_URL = 'http://localhost:3000';
const GATEWAY_WS_URL = 'ws://localhost:3001';
const TEST_USER = {
  username: 'test_proxy_user',
  email: '<EMAIL>',
  password: 'TestProxy123!',
};

class GatewayProxyTester {
  constructor() {
    this.ws = null;
    this.testResults = [];
    this.accessToken = null;
    this.refreshToken = null;
    this.userId = null;
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始Gateway认证代理功能测试');
    console.log('=' .repeat(60));

    try {
      // 阶段1：基础连接测试
      await this.testBasicConnection();
      
      // 阶段2：用户认证代理测试
      await this.testAuthenticationProxy();
      
      // 阶段3：Token管理代理测试
      await this.testTokenManagementProxy();
      
      // 阶段4：用户管理代理测试
      await this.testUserManagementProxy();
      
      // 阶段5：错误处理测试
      await this.testErrorHandling();
      
      // 阶段6：缓存机制测试
      await this.testCachingMechanism();

      // 输出测试结果
      this.printTestResults();
      
    } catch (error) {
      console.error('❌ 测试执行失败:', error.message);
    } finally {
      if (this.ws) {
        this.ws.close();
      }
    }
  }

  /**
   * 测试基础连接
   */
  async testBasicConnection() {
    console.log('\n📡 阶段1：基础连接测试');
    
    // 测试HTTP连接
    try {
      const response = await axios.get(`${GATEWAY_HTTP_URL}/health`);
      this.recordTest('HTTP连接', response.status === 200, `状态码: ${response.status}`);
    } catch (error) {
      this.recordTest('HTTP连接', false, `连接失败: ${error.message}`);
    }

    // 测试WebSocket连接
    try {
      await this.connectWebSocket();
      this.recordTest('WebSocket连接', true, '连接成功');
    } catch (error) {
      this.recordTest('WebSocket连接', false, `连接失败: ${error.message}`);
    }
  }

  /**
   * 测试用户认证代理
   */
  async testAuthenticationProxy() {
    console.log('\n🔐 阶段2：用户认证代理测试');

    // 测试用户注册代理
    try {
      const registerResponse = await axios.post(`${GATEWAY_HTTP_URL}/api/auth/register`, {
        username: TEST_USER.username,
        email: TEST_USER.email,
        password: TEST_USER.password,
      });
      
      this.recordTest('用户注册代理', registerResponse.data.success, 
        `响应: ${registerResponse.data.message}`);
    } catch (error) {
      // 用户可能已存在，这是正常的
      this.recordTest('用户注册代理', true, '用户可能已存在（正常）');
    }

    // 测试用户登录代理
    try {
      const loginResponse = await axios.post(`${GATEWAY_HTTP_URL}/api/auth/login`, {
        identifier: TEST_USER.username,
        password: TEST_USER.password,
      });

      if (loginResponse.data.success) {
        this.accessToken = loginResponse.data.data.accessToken;
        this.refreshToken = loginResponse.data.data.refreshToken;
        this.userId = loginResponse.data.data.user.id;
        
        this.recordTest('用户登录代理', true, 
          `Token获取成功，用户ID: ${this.userId}`);
      } else {
        this.recordTest('用户登录代理', false, 
          `登录失败: ${loginResponse.data.message}`);
      }
    } catch (error) {
      this.recordTest('用户登录代理', false, `登录异常: ${error.message}`);
    }

    // 测试Token验证代理
    if (this.accessToken) {
      try {
        const verifyResponse = await axios.post(`${GATEWAY_HTTP_URL}/api/auth/verify-token`, {
          token: this.accessToken,
        });

        this.recordTest('Token验证代理', verifyResponse.data.success && verifyResponse.data.data.valid,
          `验证结果: ${verifyResponse.data.data.valid ? '有效' : '无效'}`);
      } catch (error) {
        this.recordTest('Token验证代理', false, `验证异常: ${error.message}`);
      }
    }
  }

  /**
   * 测试Token管理代理
   */
  async testTokenManagementProxy() {
    console.log('\n🎫 阶段3：Token管理代理测试');

    // 测试Token刷新代理
    if (this.refreshToken) {
      try {
        const refreshResponse = await axios.post(`${GATEWAY_HTTP_URL}/api/auth/refresh`, {
          refreshToken: this.refreshToken,
        });

        if (refreshResponse.data.success) {
          const newAccessToken = refreshResponse.data.data.accessToken;
          this.recordTest('Token刷新代理', !!newAccessToken, 
            `新Token获取: ${newAccessToken ? '成功' : '失败'}`);
          
          // 更新Token用于后续测试
          if (newAccessToken) {
            this.accessToken = newAccessToken;
          }
        } else {
          this.recordTest('Token刷新代理', false, 
            `刷新失败: ${refreshResponse.data.message}`);
        }
      } catch (error) {
        this.recordTest('Token刷新代理', false, `刷新异常: ${error.message}`);
      }
    }

    // 测试用户信息获取（需要Token）
    if (this.accessToken) {
      try {
        const userInfoResponse = await axios.post(`${GATEWAY_HTTP_URL}/api/auth/user-info`, {}, {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`
          }
        });

        this.recordTest('用户信息获取代理', userInfoResponse.data.success,
          `用户信息: ${userInfoResponse.data.success ? '获取成功' : '获取失败'}`);
      } catch (error) {
        this.recordTest('用户信息获取代理', false, `获取异常: ${error.message}`);
      }
    }
  }

  /**
   * 测试用户管理代理
   */
  async testUserManagementProxy() {
    console.log('\n👤 阶段4：用户管理代理测试');

    if (!this.accessToken || !this.userId) {
      this.recordTest('用户管理代理测试', false, '缺少必要的认证信息');
      return;
    }

    // 测试获取用户资料代理（通过微服务调用）
    try {
      const profileResponse = await this.callMicroservice('getUserProfile', {
        userId: this.userId
      });

      this.recordTest('获取用户资料代理', profileResponse.success,
        `资料获取: ${profileResponse.success ? '成功' : profileResponse.error}`);
    } catch (error) {
      this.recordTest('获取用户资料代理', false, `调用异常: ${error.message}`);
    }

    // 测试修改密码代理（通过微服务调用）
    try {
      const changePasswordResponse = await this.callMicroservice('changePassword', {
        userId: this.userId,
        oldPassword: TEST_USER.password,
        newPassword: TEST_USER.password + '_new'
      });

      // 如果成功，再改回原密码
      if (changePasswordResponse.success) {
        await this.callMicroservice('changePassword', {
          userId: this.userId,
          oldPassword: TEST_USER.password + '_new',
          newPassword: TEST_USER.password
        });
      }

      this.recordTest('修改密码代理', changePasswordResponse.success,
        `密码修改: ${changePasswordResponse.success ? '成功' : changePasswordResponse.error}`);
    } catch (error) {
      this.recordTest('修改密码代理', false, `调用异常: ${error.message}`);
    }
  }

  /**
   * 测试错误处理
   */
  async testErrorHandling() {
    console.log('\n⚠️  阶段5：错误处理测试');

    // 测试无效Token
    try {
      const invalidTokenResponse = await axios.post(`${GATEWAY_HTTP_URL}/api/auth/verify-token`, {
        token: 'invalid_token_12345'
      });

      this.recordTest('无效Token处理', !invalidTokenResponse.data.success,
        `错误处理: ${invalidTokenResponse.data.success ? '未正确识别无效Token' : '正确识别无效Token'}`);
    } catch (error) {
      this.recordTest('无效Token处理', true, '正确抛出异常');
    }

    // 测试无效登录
    try {
      const invalidLoginResponse = await axios.post(`${GATEWAY_HTTP_URL}/api/auth/login`, {
        identifier: 'nonexistent_user',
        password: 'wrong_password'
      });

      this.recordTest('无效登录处理', !invalidLoginResponse.data.success,
        `错误处理: ${invalidLoginResponse.data.success ? '未正确识别无效登录' : '正确识别无效登录'}`);
    } catch (error) {
      this.recordTest('无效登录处理', true, '正确抛出异常');
    }
  }

  /**
   * 测试缓存机制
   */
  async testCachingMechanism() {
    console.log('\n💾 阶段6：缓存机制测试');

    if (!this.accessToken) {
      this.recordTest('缓存机制测试', false, '缺少必要的Token');
      return;
    }

    // 连续两次Token验证，测试缓存
    const startTime1 = Date.now();
    try {
      await axios.post(`${GATEWAY_HTTP_URL}/api/auth/verify-token`, {
        token: this.accessToken
      });
      const time1 = Date.now() - startTime1;

      const startTime2 = Date.now();
      await axios.post(`${GATEWAY_HTTP_URL}/api/auth/verify-token`, {
        token: this.accessToken
      });
      const time2 = Date.now() - startTime2;

      // 第二次应该更快（缓存命中）
      const isCacheWorking = time2 < time1 * 0.8; // 缓存应该至少快20%
      
      this.recordTest('Token验证缓存', isCacheWorking,
        `第一次: ${time1}ms, 第二次: ${time2}ms, 缓存${isCacheWorking ? '生效' : '未生效'}`);
    } catch (error) {
      this.recordTest('Token验证缓存', false, `缓存测试异常: ${error.message}`);
    }
  }

  /**
   * 连接WebSocket
   */
  async connectWebSocket() {
    return new Promise((resolve, reject) => {
      this.ws = new WebSocket(GATEWAY_WS_URL);

      this.ws.on('open', () => {
        console.log('✅ WebSocket连接成功');
        resolve();
      });

      this.ws.on('error', (error) => {
        console.error('❌ WebSocket连接失败:', error.message);
        reject(error);
      });

      this.ws.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          console.log('📨 收到WebSocket消息:', message);
        } catch (error) {
          console.log('📨 收到WebSocket原始消息:', data.toString());
        }
      });
    });
  }

  /**
   * 调用微服务（通过WebSocket）
   */
  async callMicroservice(pattern, data) {
    return new Promise((resolve, reject) => {
      if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
        reject(new Error('WebSocket未连接'));
        return;
      }

      const requestId = Date.now().toString();
      const message = {
        pattern,
        data,
        id: requestId
      };

      // 设置响应监听器
      const responseHandler = (responseData) => {
        try {
          const response = JSON.parse(responseData);
          if (response.id === requestId) {
            this.ws.removeListener('message', responseHandler);
            resolve(response);
          }
        } catch (error) {
          // 忽略解析错误，可能是其他消息
        }
      };

      this.ws.on('message', responseHandler);

      // 发送请求
      this.ws.send(JSON.stringify(message));

      // 设置超时
      setTimeout(() => {
        this.ws.removeListener('message', responseHandler);
        reject(new Error('微服务调用超时'));
      }, 10000);
    });
  }

  /**
   * 记录测试结果
   */
  recordTest(testName, success, details = '') {
    const result = {
      name: testName,
      success,
      details,
      timestamp: new Date().toISOString()
    };

    this.testResults.push(result);

    const status = success ? '✅' : '❌';
    console.log(`  ${status} ${testName}: ${details}`);
  }

  /**
   * 打印测试结果汇总
   */
  printTestResults() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 Gateway认证代理功能测试结果汇总');
    console.log('='.repeat(60));

    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;

    console.log(`\n📈 总体统计:`);
    console.log(`  总测试数: ${totalTests}`);
    console.log(`  通过数: ${passedTests} ✅`);
    console.log(`  失败数: ${failedTests} ❌`);
    console.log(`  通过率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    if (failedTests > 0) {
      console.log(`\n❌ 失败的测试:`);
      this.testResults
        .filter(r => !r.success)
        .forEach(r => {
          console.log(`  - ${r.name}: ${r.details}`);
        });
    }

    console.log(`\n🎯 代理功能验证结果:`);
    if (passedTests >= totalTests * 0.8) {
      console.log('✅ Gateway认证代理功能基本正常，可以进入阶段四');
    } else {
      console.log('❌ Gateway认证代理功能存在问题，需要修复后再进入阶段四');
    }
  }
}

// 运行测试
if (require.main === module) {
  const tester = new GatewayProxyTester();
  tester.runAllTests().catch(console.error);
}

module.exports = GatewayProxyTester;
