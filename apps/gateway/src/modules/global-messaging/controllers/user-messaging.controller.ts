import { Controller, Get, Post, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

// 守卫
import { AuthGuard } from '../../gateway-auth/guards/auth.guard';

// 装饰器
import { AccountToken } from '../../websocket/decorators/token-scope.decorator';

// 服务
import { GlobalBroadcastService } from '../services/global-broadcast.service';
import { MessagePersistenceService } from '../services/message-persistence.service';

// 接口定义
import { GlobalMessageType, MessagePriority } from '../interfaces/global-message.interface';

/**
 * 用户消息控制器
 * 提供用户查看全服消息的接口
 * 
 * 核心功能：
 * 1. 消息查看：用户查看全服消息
 * 2. 消息确认：用户确认已读消息
 * 3. 消息历史：查看历史消息记录
 * 4. 个人统计：查看个人消息统计
 */
@ApiTags('User Global Messaging')
@ApiBearerAuth()
@Controller('user/global-messages')
@UseGuards(AuthGuard)
export class UserMessagingController {
  constructor(
    private readonly globalBroadcastService: GlobalBroadcastService,
    private readonly messagePersistenceService: MessagePersistenceService,
  ) {}

  /**
   * 获取当前活跃的全服消息
   */
  @Get('active')
  @AccountToken() // 使用账号Token即可查看
  @ApiOperation({ summary: '获取当前活跃的全服消息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getActiveMessages(
    @Query('type') type?: GlobalMessageType,
    @Query('limit') limit?: number,
  ): Promise<any> {
    const now = new Date();
    const filters = {
      type,
      startDate: new Date(now.getTime() - 24 * 60 * 60 * 1000), // 最近24小时
      endDate: now,
      limit: limit || 20,
    };

    const messages = await this.messagePersistenceService.getMessageHistory(filters);
    
    // 过滤出仍然活跃的消息（未过期）
    const activeMessages = messages.filter(message => 
      !message.expireAt || new Date(message.expireAt) > now
    );

    return {
      success: true,
      message: '获取活跃消息成功',
      data: {
        messages: activeMessages,
        total: activeMessages.length,
      },
    };
  }

  /**
   * 获取消息历史
   */
  @Get('history')
  @AccountToken()
  @ApiOperation({ summary: '获取消息历史' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getMessageHistory(
    @Query('type') type?: GlobalMessageType,
    @Query('priority') priority?: MessagePriority,
    @Query('days') days?: number,
    @Query('limit') limit?: number,
  ): Promise<any> {
    const daysBack = days || 7; // 默认查看最近7天
    const now = new Date();
    const startDate = new Date(now.getTime() - daysBack * 24 * 60 * 60 * 1000);

    const filters = {
      type,
      priority,
      startDate,
      endDate: now,
      limit: limit || 50,
    };

    const messages = await this.messagePersistenceService.getMessageHistory(filters);

    return {
      success: true,
      message: '获取消息历史成功',
      data: {
        messages,
        total: messages.length,
        filters: {
          ...filters,
          daysBack,
        },
      },
    };
  }

  /**
   * 获取消息详情
   */
  @Get(':messageId')
  @AccountToken()
  @ApiOperation({ summary: '获取消息详情' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getMessage(@Param('messageId') messageId: string): Promise<any> {
    const message = await this.messagePersistenceService.getMessage(messageId);
    
    if (!message) {
      return {
        success: false,
        message: '消息不存在',
        data: null,
      };
    }

    // 用户只能看到消息内容，不能看到投递记录等敏感信息
    const userMessage = {
      id: message.id,
      type: message.type,
      priority: message.priority,
      title: message.title,
      content: message.content,
      createdAt: message.createdAt,
      publishAt: message.publishAt,
      expireAt: message.expireAt,
      metadata: {
        displayType: message.metadata?.displayType,
        autoClose: message.metadata?.autoClose,
        actionButton: message.metadata?.actionButton,
      },
    };

    return {
      success: true,
      message: '获取消息详情成功',
      data: userMessage,
    };
  }

  /**
   * 确认消息已读
   */
  @Post(':messageId/acknowledge')
  @AccountToken()
  @ApiOperation({ summary: '确认消息已读' })
  @ApiResponse({ status: 200, description: '确认成功' })
  async acknowledgeMessage(
    @Param('messageId') messageId: string,
    @Body() userData: { userId: string },
  ): Promise<any> {
    const message = await this.messagePersistenceService.getMessage(messageId);
    
    if (!message) {
      return {
        success: false,
        message: '消息不存在',
        data: null,
      };
    }

    // 记录用户确认
    const deliveryRecord = {
      id: `delivery_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      messageId,
      userId: userData.userId,
      serverId: 'global', // 全服消息
      status: 'acknowledged' as const,
      deliveredAt: new Date(),
      acknowledgedAt: new Date(),
      retryCount: 0,
    };

    await this.messagePersistenceService.recordDelivery(deliveryRecord);

    return {
      success: true,
      message: '消息确认成功',
      data: {
        messageId,
        acknowledgedAt: deliveryRecord.acknowledgedAt,
      },
    };
  }

  /**
   * 获取用户消息统计
   */
  @Get('stats/personal')
  @AccountToken()
  @ApiOperation({ summary: '获取个人消息统计' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getPersonalStats(@Query('userId') userId: string): Promise<any> {
    // 这里应该实现用户个人的消息统计
    // 包括已读消息数、未读消息数、各类型消息统计等
    
    const stats = {
      totalMessages: 0,
      readMessages: 0,
      unreadMessages: 0,
      typeBreakdown: {
        [GlobalMessageType.SYSTEM_ANNOUNCEMENT]: 0,
        [GlobalMessageType.EVENT_NOTIFICATION]: 0,
        [GlobalMessageType.MAINTENANCE_NOTICE]: 0,
        [GlobalMessageType.EMERGENCY_ALERT]: 0,
      },
      lastReadTime: null,
    };

    // TODO: 实现实际的统计逻辑
    // 需要查询用户的消息投递记录和确认记录

    return {
      success: true,
      message: '获取个人统计成功',
      data: stats,
    };
  }

  /**
   * 获取未读消息数量
   */
  @Get('stats/unread-count')
  @AccountToken()
  @ApiOperation({ summary: '获取未读消息数量' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getUnreadCount(@Query('userId') userId: string): Promise<any> {
    // 获取最近的活跃消息
    const now = new Date();
    const messages = await this.messagePersistenceService.getMessageHistory({
      startDate: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000), // 最近7天
      endDate: now,
      limit: 100,
    });

    // 过滤出仍然活跃的消息
    const activeMessages = messages.filter(message => 
      !message.expireAt || new Date(message.expireAt) > now
    );

    // TODO: 实现实际的未读统计逻辑
    // 需要查询用户对这些消息的确认记录
    const unreadCount = activeMessages.length; // 暂时简化处理

    return {
      success: true,
      message: '获取未读数量成功',
      data: {
        unreadCount,
        totalActiveMessages: activeMessages.length,
      },
    };
  }

  /**
   * 批量确认消息
   */
  @Post('acknowledge-batch')
  @AccountToken()
  @ApiOperation({ summary: '批量确认消息' })
  @ApiResponse({ status: 200, description: '批量确认成功' })
  async acknowledgeBatch(
    @Body() batchData: { userId: string; messageIds: string[] },
  ): Promise<any> {
    const results = {
      success: 0,
      failed: 0,
      errors: [],
    };

    for (const messageId of batchData.messageIds) {
      try {
        const deliveryRecord = {
          id: `delivery_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          messageId,
          userId: batchData.userId,
          serverId: 'global',
          status: 'acknowledged' as const,
          deliveredAt: new Date(),
          acknowledgedAt: new Date(),
          retryCount: 0,
        };

        await this.messagePersistenceService.recordDelivery(deliveryRecord);
        results.success++;
      } catch (error) {
        results.failed++;
        results.errors.push({
          messageId,
          error: error.message,
        });
      }
    }

    return {
      success: true,
      message: '批量确认完成',
      data: results,
    };
  }
}
