import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { RedisModule } from '@common/redis';

// JWT共享模块
import { JwtSharedModule } from '@gateway/infra/jwt/jwt-shared.module';

// 认证模块
import { GatewayAuthModule } from '../gateway-auth/gateway-auth.module';

// 控制器
import { GlobalMessagingController } from './controllers/global-messaging.controller';
import { AdminMessagingController } from './controllers/admin-messaging.controller';
import { UserMessagingController } from './controllers/user-messaging.controller';

// 服务
import { GlobalBroadcastService } from './services/global-broadcast.service';
import { MessagePublisherService } from './services/message-publisher.service';
import { MessageSchedulerService } from './services/message-scheduler.service';
import { MessagePersistenceService } from './services/message-persistence.service';
import { AnnouncementService } from './services/announcement.service';
import { EventNotificationService } from './services/event-notification.service';

// 队列系统
import { QueueManagerService } from './queue/queue-manager.service';
import { PriorityQueueService } from './queue/priority-queue.service';
import { DeadLetterQueueService } from './queue/dead-letter-queue.service';
import { QueueMonitorService } from './queue/queue-monitor.service';

/**
 * 全局消息功能模块
 *
 * 职责：
 * - 提供全服消息广播功能
 * - 管理消息队列和优先级
 * - 处理消息持久化和重发
 *
 * 依赖：
 * - SharedModule：Redis缓存、微服务通信
 * - GatewayAuthModule：管理员认证
 */
@Module({
  imports: [
    ConfigModule,
    RedisModule,
    JwtSharedModule,
    GatewayAuthModule,
  ],
  controllers: [
    GlobalMessagingController,
    AdminMessagingController,
    UserMessagingController,
  ],
  providers: [
    // 核心服务
    GlobalBroadcastService,
    MessagePublisherService,
    MessageSchedulerService,
    MessagePersistenceService,
    AnnouncementService,
    EventNotificationService,

    // 队列系统
    QueueManagerService,
    PriorityQueueService,
    DeadLetterQueueService,
    QueueMonitorService,
  ],
  exports: [
    GlobalBroadcastService,
    MessagePublisherService,
    AnnouncementService,
    EventNotificationService,
    QueueManagerService,
  ],
})
export class GlobalMessagingModule {}
