import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

// 核心服务
import { RedisService } from '@common/redis';

// 接口定义
import { BaseGlobalMessage } from '../interfaces/global-message.interface';

/**
 * 死信队列服务
 * 处理发送失败或无法处理的消息
 * 
 * 核心功能：
 * 1. 失败消息收集：收集处理失败的消息
 * 2. 重试机制：支持消息重试处理
 * 3. 失败分析：分析失败原因和模式
 * 4. 手动恢复：支持手动恢复失败的消息
 * 5. 清理机制：定期清理过期的死信消息
 */
@Injectable()
export class DeadLetterQueueService {
  private readonly logger = new Logger(DeadLetterQueueService.name);
  private readonly deadLetterQueueKey = 'global_messages:dead_letter';

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
  ) {}

  /**
   * 将消息添加到死信队列
   */
  async addToDeadLetterQueue(
    message: BaseGlobalMessage,
    errorReason: string,
    retryCount: number = 0
  ): Promise<void> {
    this.logger.warn(`💀 Adding message to dead letter queue: ${message.id}, reason: ${errorReason}`);

    try {
      const deadLetterEntry = {
        id: `dl_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        originalMessage: message,
        errorReason,
        retryCount,
        addedAt: new Date(),
        lastRetryAt: retryCount > 0 ? new Date() : null,
      };

      await this.redisService.lpush(
        this.deadLetterQueueKey,
        JSON.stringify(deadLetterEntry),
        'global'
      );

      // 记录死信统计
      await this.updateDeadLetterStats('added', errorReason);

      this.logger.warn(`💀 Message added to dead letter queue: ${message.id}`);

    } catch (error) {
      this.logger.error(`❌ Failed to add message to dead letter queue: ${message.id}`, error);
      throw error;
    }
  }

  /**
   * 从死信队列获取消息
   */
  async getDeadLetterMessages(limit: number = 10): Promise<any[]> {
    try {
      const messages = await this.redisService.lrange(
        this.deadLetterQueueKey,
        0,
        limit - 1,
        'global'
      );

      const deadLetterMessages = [];
      for (const messageData of messages) {
        try {
          deadLetterMessages.push(JSON.parse(messageData as string));
        } catch (parseError) {
          this.logger.error('❌ Failed to parse dead letter message', parseError);
        }
      }

      return deadLetterMessages;

    } catch (error) {
      this.logger.error('❌ Failed to get dead letter messages', error);
      return [];
    }
  }

  /**
   * 重试死信消息
   */
  async retryDeadLetterMessage(deadLetterEntryId: string): Promise<boolean> {
    this.logger.log(`🔄 Retrying dead letter message: ${deadLetterEntryId}`);

    try {
      const messages = await this.redisService.lrange(this.deadLetterQueueKey, 0, -1, 'global');
      
      for (let i = 0; i < messages.length; i++) {
        try {
          const deadLetterEntry = JSON.parse(messages[i] as string);
          
          if (deadLetterEntry.id === deadLetterEntryId) {
            // 检查重试次数限制
            const maxRetries = this.configService.get<number>('gateway.globalMessaging.maxRetries', 3);
            
            if (deadLetterEntry.retryCount >= maxRetries) {
              this.logger.warn(`⚠️ Max retries exceeded for message: ${deadLetterEntryId}`);
              return false;
            }

            // 更新重试信息
            deadLetterEntry.retryCount += 1;
            deadLetterEntry.lastRetryAt = new Date();

            // 尝试重新处理消息
            const success = await this.reprocessMessage(deadLetterEntry.originalMessage);
            
            if (success) {
              // 成功处理，从死信队列移除
              await this.redisService.lrem(this.deadLetterQueueKey, 1, messages[i], 'global');
              await this.updateDeadLetterStats('recovered', 'retry_success');
              
              this.logger.log(`✅ Dead letter message recovered: ${deadLetterEntryId}`);
              return true;
            } else {
              // 重试失败，更新死信记录
              await this.redisService.lset(
                this.deadLetterQueueKey,
                i,
                JSON.stringify(deadLetterEntry),
                'global'
              );
              
              await this.updateDeadLetterStats('retry_failed', 'reprocess_failed');
              this.logger.warn(`❌ Dead letter message retry failed: ${deadLetterEntryId}`);
              return false;
            }
          }
        } catch (parseError) {
          this.logger.error('❌ Failed to parse dead letter entry', parseError);
        }
      }

      this.logger.warn(`⚠️ Dead letter entry not found: ${deadLetterEntryId}`);
      return false;

    } catch (error) {
      this.logger.error(`❌ Failed to retry dead letter message: ${deadLetterEntryId}`, error);
      return false;
    }
  }

  /**
   * 批量重试死信消息
   */
  async retryAllDeadLetterMessages(): Promise<{ success: number; failed: number }> {
    this.logger.log('🔄 Retrying all dead letter messages');

    try {
      const deadLetterMessages = await this.getDeadLetterMessages(100); // 限制批量处理数量
      const results = { success: 0, failed: 0 };

      for (const deadLetterEntry of deadLetterMessages) {
        const success = await this.retryDeadLetterMessage(deadLetterEntry.id);
        if (success) {
          results.success++;
        } else {
          results.failed++;
        }
      }

      this.logger.log(`📊 Batch retry completed: ${results.success} success, ${results.failed} failed`);
      return results;

    } catch (error) {
      this.logger.error('❌ Failed to retry all dead letter messages', error);
      return { success: 0, failed: 0 };
    }
  }

  /**
   * 删除死信消息
   */
  async removeDeadLetterMessage(deadLetterEntryId: string): Promise<boolean> {
    this.logger.log(`🗑️ Removing dead letter message: ${deadLetterEntryId}`);

    try {
      const messages = await this.redisService.lrange(this.deadLetterQueueKey, 0, -1, 'global');
      
      for (const messageData of messages) {
        try {
          const deadLetterEntry = JSON.parse(messageData as string);
          
          if (deadLetterEntry.id === deadLetterEntryId) {
            await this.redisService.lrem(this.deadLetterQueueKey, 1, messageData, 'global');
            await this.updateDeadLetterStats('removed', 'manual_removal');
            
            this.logger.log(`✅ Dead letter message removed: ${deadLetterEntryId}`);
            return true;
          }
        } catch (parseError) {
          this.logger.error('❌ Failed to parse dead letter entry', parseError);
        }
      }

      this.logger.warn(`⚠️ Dead letter entry not found: ${deadLetterEntryId}`);
      return false;

    } catch (error) {
      this.logger.error(`❌ Failed to remove dead letter message: ${deadLetterEntryId}`, error);
      return false;
    }
  }

  /**
   * 清理过期的死信消息
   */
  async cleanupExpiredDeadLetterMessages(): Promise<number> {
    this.logger.log('🧹 Cleaning up expired dead letter messages');

    try {
      const messages = await this.redisService.lrange(this.deadLetterQueueKey, 0, -1, 'global');
      const retentionDays = this.configService.get<number>('gateway.globalMessaging.deadLetterRetentionDays', 7);
      const cutoffDate = new Date(Date.now() - retentionDays * 24 * 60 * 60 * 1000);
      
      let cleanedCount = 0;

      for (const messageData of messages) {
        try {
          const deadLetterEntry = JSON.parse(messageData as string);
          const addedAt = new Date(deadLetterEntry.addedAt);
          
          if (addedAt < cutoffDate) {
            await this.redisService.lrem(this.deadLetterQueueKey, 1, messageData, 'global');
            cleanedCount++;
            this.logger.debug(`🗑️ Expired dead letter message cleaned: ${deadLetterEntry.id}`);
          }
        } catch (parseError) {
          this.logger.error('❌ Failed to parse dead letter entry during cleanup', parseError);
        }
      }

      if (cleanedCount > 0) {
        this.logger.log(`✅ Dead letter cleanup completed: ${cleanedCount} messages removed`);
      }

      return cleanedCount;

    } catch (error) {
      this.logger.error('❌ Error during dead letter messages cleanup', error);
      return 0;
    }
  }

  /**
   * 获取死信队列统计
   */
  async getDeadLetterStats(): Promise<any> {
    try {
      const stats = {
        totalMessages: 0,
        errorReasons: {},
        retryStats: {
          totalRetries: 0,
          successfulRetries: 0,
          failedRetries: 0,
        },
        oldestMessage: null,
        newestMessage: null,
      };

      // 获取队列大小
      stats.totalMessages = await this.redisService.llen(this.deadLetterQueueKey, 'global');

      if (stats.totalMessages > 0) {
        const messages = await this.getDeadLetterMessages(stats.totalMessages);
        
        // 分析错误原因
        const errorCounts = {};
        let oldestDate = null;
        let newestDate = null;

        for (const deadLetterEntry of messages) {
          // 统计错误原因
          const reason = deadLetterEntry.errorReason || 'unknown';
          errorCounts[reason] = (errorCounts[reason] || 0) + 1;

          // 统计重试次数
          stats.retryStats.totalRetries += deadLetterEntry.retryCount || 0;

          // 找出最旧和最新的消息
          const addedAt = new Date(deadLetterEntry.addedAt);
          if (!oldestDate || addedAt < oldestDate) {
            oldestDate = addedAt;
          }
          if (!newestDate || addedAt > newestDate) {
            newestDate = addedAt;
          }
        }

        stats.errorReasons = errorCounts;
        stats.oldestMessage = oldestDate;
        stats.newestMessage = newestDate;
      }

      // 获取重试统计（从Redis获取）
      const retryStatsKey = 'dead_letter_stats:retry';
      const retryStatsData = await this.redisService.get(retryStatsKey, 'global');
      if (retryStatsData) {
        const retryStats = JSON.parse(retryStatsData as string);
        stats.retryStats.successfulRetries = retryStats.successful || 0;
        stats.retryStats.failedRetries = retryStats.failed || 0;
      }

      return stats;

    } catch (error) {
      this.logger.error('❌ Failed to get dead letter stats', error);
      return null;
    }
  }

  /**
   * 重新处理消息
   */
  private async reprocessMessage(message: BaseGlobalMessage): Promise<boolean> {
    try {
      // 这里应该调用消息发布服务重新处理消息
      // 暂时简化处理，实际实现时需要注入MessagePublisherService
      this.logger.debug(`🔄 Reprocessing message: ${message.id}`);
      
      // 模拟重新处理
      // await this.messagePublisherService.publishMessage(message);
      
      return true; // 暂时返回成功

    } catch (error) {
      this.logger.error(`❌ Failed to reprocess message: ${message.id}`, error);
      return false;
    }
  }

  /**
   * 更新死信统计
   */
  private async updateDeadLetterStats(operation: string, reason: string): Promise<void> {
    const statsKey = 'dead_letter_stats:operations';
    
    try {
      const stats = {
        added: 0,
        recovered: 0,
        removed: 0,
        retry_failed: 0,
        lastOperation: new Date(),
        reasons: {},
      };

      // 获取现有统计
      const existingStatsData = await this.redisService.get(statsKey, 'global');
      if (existingStatsData) {
        Object.assign(stats, JSON.parse(existingStatsData as string));
      }

      // 更新统计
      if (stats[operation] !== undefined) {
        stats[operation] += 1;
      }

      // 更新原因统计
      if (!stats.reasons[reason]) {
        stats.reasons[reason] = 0;
      }
      stats.reasons[reason] += 1;

      stats.lastOperation = new Date();

      await this.redisService.set(statsKey, JSON.stringify(stats), 3600 * 24 * 30, 'global');

    } catch (error) {
      this.logger.error('❌ Failed to update dead letter stats', error);
    }
  }
}
