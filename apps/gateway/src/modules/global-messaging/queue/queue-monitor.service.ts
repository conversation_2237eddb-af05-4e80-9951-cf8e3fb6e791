import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';

// 核心服务
import { RedisService } from '@common/redis';

// 其他服务
import { QueueManagerService } from './queue-manager.service';
import { PriorityQueueService } from './priority-queue.service';
import { DeadLetterQueueService } from './dead-letter-queue.service';

/**
 * 队列监控服务
 * 监控队列系统的健康状态和性能指标
 * 
 * 核心功能：
 * 1. 队列健康监控：监控各队列的健康状态
 * 2. 性能指标收集：收集队列处理性能数据
 * 3. 告警机制：在队列异常时发送告警
 * 4. 自动恢复：尝试自动恢复队列问题
 * 5. 监控报告：生成队列监控报告
 */
@Injectable()
export class QueueMonitorService {
  private readonly logger = new Logger(QueueMonitorService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly queueManagerService: QueueManagerService,
    private readonly priorityQueueService: PriorityQueueService,
    private readonly deadLetterQueueService: DeadLetterQueueService,
  ) {}

  /**
   * 定时监控队列健康状态
   * 每5分钟执行一次
   */
  @Cron('0 */5 * * * *')
  async monitorQueueHealth(): Promise<void> {
    this.logger.debug('🔍 Starting queue health monitoring');

    try {
      const healthReport = await this.generateHealthReport();
      
      // 检查是否有严重问题
      if (healthReport.overallStatus === 'critical') {
        await this.handleCriticalIssues(healthReport);
      } else if (healthReport.overallStatus === 'warning') {
        await this.handleWarningIssues(healthReport);
      }

      // 记录监控结果
      await this.recordMonitoringResult(healthReport);

      this.logger.debug(`✅ Queue health monitoring completed: ${healthReport.overallStatus}`);

    } catch (error) {
      this.logger.error('❌ Error during queue health monitoring', error);
    }
  }

  /**
   * 生成健康报告
   */
  async generateHealthReport(): Promise<any> {
    this.logger.debug('📊 Generating queue health report');

    try {
      const report = {
        timestamp: new Date(),
        overallStatus: 'healthy',
        issues: [],
        metrics: {
          totalMessages: 0,
          deadLetterMessages: 0,
          priorityDistribution: {},
          avgWaitTime: 0,
        },
        recommendations: [],
      };

      // 获取队列统计
      const queueStats = await this.queueManagerService.getQueueStats();
      const priorityHealth = await this.priorityQueueService.getPriorityQueueHealth();
      const deadLetterStats = await this.deadLetterQueueService.getDeadLetterStats();

      report.metrics.totalMessages = queueStats?.totalMessages || 0;
      report.metrics.deadLetterMessages = deadLetterStats?.totalMessages || 0;
      report.metrics.priorityDistribution = queueStats?.priorityBreakdown || {};
      report.metrics.avgWaitTime = queueStats?.avgWaitTime || 0;

      // 检查队列积压
      if (report.metrics.totalMessages > 1000) {
        report.overallStatus = 'warning';
        report.issues.push('High message backlog detected');
        report.recommendations.push('Consider increasing message processing capacity');
      }

      if (report.metrics.totalMessages > 5000) {
        report.overallStatus = 'critical';
        report.issues.push('Critical message backlog detected');
        report.recommendations.push('Immediate action required to clear backlog');
      }

      // 检查死信队列
      if (report.metrics.deadLetterMessages > 100) {
        report.overallStatus = report.overallStatus === 'critical' ? 'critical' : 'warning';
        report.issues.push('High number of dead letter messages');
        report.recommendations.push('Review and retry dead letter messages');
      }

      // 检查平均等待时间
      if (report.metrics.avgWaitTime > 300000) { // 5分钟
        report.overallStatus = report.overallStatus === 'critical' ? 'critical' : 'warning';
        report.issues.push('High average message wait time');
        report.recommendations.push('Optimize message processing pipeline');
      }

      // 合并优先级队列健康状态
      if (priorityHealth && priorityHealth.status !== 'healthy') {
        report.overallStatus = priorityHealth.status === 'error' ? 'critical' : 
                              (report.overallStatus === 'critical' ? 'critical' : priorityHealth.status);
        report.issues.push(...priorityHealth.issues);
        report.recommendations.push(...priorityHealth.recommendations);
      }

      return report;

    } catch (error) {
      this.logger.error('❌ Failed to generate health report', error);
      return {
        timestamp: new Date(),
        overallStatus: 'error',
        issues: ['Failed to generate health report'],
        metrics: {},
        recommendations: ['Check system logs and connectivity'],
      };
    }
  }

  /**
   * 获取队列性能指标
   */
  async getPerformanceMetrics(): Promise<any> {
    try {
      const metrics = {
        throughput: {
          messagesPerSecond: 0,
          messagesPerMinute: 0,
          messagesPerHour: 0,
        },
        latency: {
          avgProcessingTime: 0,
          p95ProcessingTime: 0,
          p99ProcessingTime: 0,
        },
        errorRates: {
          totalErrors: 0,
          errorRate: 0,
          deadLetterRate: 0,
        },
        capacity: {
          currentLoad: 0,
          maxCapacity: 10000, // 可配置
          utilizationRate: 0,
        },
      };

      // 获取吞吐量数据
      const throughputData = await this.getThroughputData();
      if (throughputData) {
        metrics.throughput = throughputData;
      }

      // 获取延迟数据
      const latencyData = await this.getLatencyData();
      if (latencyData) {
        metrics.latency = latencyData;
      }

      // 获取错误率数据
      const errorData = await this.getErrorRateData();
      if (errorData) {
        metrics.errorRates = errorData;
      }

      // 计算容量利用率
      const queueStats = await this.queueManagerService.getQueueStats();
      if (queueStats) {
        metrics.capacity.currentLoad = queueStats.totalMessages || 0;
        metrics.capacity.utilizationRate = metrics.capacity.currentLoad / metrics.capacity.maxCapacity;
      }

      return metrics;

    } catch (error) {
      this.logger.error('❌ Failed to get performance metrics', error);
      return null;
    }
  }

  /**
   * 检查队列是否需要扩容
   */
  async checkScalingNeeds(): Promise<any> {
    try {
      const metrics = await this.getPerformanceMetrics();
      if (!metrics) return null;

      const scalingRecommendation = {
        needsScaling: false,
        reason: '',
        recommendedAction: '',
        urgency: 'low',
      };

      // 检查容量利用率
      if (metrics.capacity.utilizationRate > 0.8) {
        scalingRecommendation.needsScaling = true;
        scalingRecommendation.reason = 'High capacity utilization';
        scalingRecommendation.recommendedAction = 'Increase queue processing capacity';
        scalingRecommendation.urgency = metrics.capacity.utilizationRate > 0.95 ? 'high' : 'medium';
      }

      // 检查处理延迟
      if (metrics.latency.avgProcessingTime > 5000) { // 5秒
        scalingRecommendation.needsScaling = true;
        scalingRecommendation.reason = 'High processing latency';
        scalingRecommendation.recommendedAction = 'Add more processing workers';
        scalingRecommendation.urgency = 'medium';
      }

      // 检查错误率
      if (metrics.errorRates.errorRate > 0.05) { // 5%
        scalingRecommendation.needsScaling = true;
        scalingRecommendation.reason = 'High error rate';
        scalingRecommendation.recommendedAction = 'Review and fix processing issues';
        scalingRecommendation.urgency = 'high';
      }

      return scalingRecommendation;

    } catch (error) {
      this.logger.error('❌ Failed to check scaling needs', error);
      return null;
    }
  }

  /**
   * 处理严重问题
   */
  private async handleCriticalIssues(healthReport: any): Promise<void> {
    this.logger.error('🚨 Critical queue issues detected', healthReport.issues);

    try {
      // 尝试自动恢复措施
      if (healthReport.issues.includes('Critical message backlog detected')) {
        await this.attemptBacklogRecovery();
      }

      if (healthReport.issues.includes('High number of dead letter messages')) {
        await this.attemptDeadLetterRecovery();
      }

      // 发送告警通知
      await this.sendAlert('critical', healthReport);

    } catch (error) {
      this.logger.error('❌ Failed to handle critical issues', error);
    }
  }

  /**
   * 处理警告问题
   */
  private async handleWarningIssues(healthReport: any): Promise<void> {
    this.logger.warn('⚠️ Queue warning issues detected', healthReport.issues);

    try {
      // 实施优先级限流
      if (healthReport.issues.includes('High average message wait time')) {
        await this.priorityQueueService.implementPriorityThrottling();
      }

      // 发送警告通知
      await this.sendAlert('warning', healthReport);

    } catch (error) {
      this.logger.error('❌ Failed to handle warning issues', error);
    }
  }

  /**
   * 尝试积压恢复
   */
  private async attemptBacklogRecovery(): Promise<void> {
    this.logger.log('🔧 Attempting backlog recovery');

    try {
      // 清理过期消息
      const expiredCount = await this.priorityQueueService.cleanupExpiredHighPriorityMessages();
      this.logger.log(`🧹 Cleaned ${expiredCount} expired messages`);

      // 实施优先级限流
      await this.priorityQueueService.implementPriorityThrottling();

    } catch (error) {
      this.logger.error('❌ Failed to attempt backlog recovery', error);
    }
  }

  /**
   * 尝试死信队列恢复
   */
  private async attemptDeadLetterRecovery(): Promise<void> {
    this.logger.log('🔧 Attempting dead letter recovery');

    try {
      // 批量重试死信消息
      const retryResults = await this.deadLetterQueueService.retryAllDeadLetterMessages();
      this.logger.log(`🔄 Retry results: ${retryResults.success} success, ${retryResults.failed} failed`);

      // 清理过期死信消息
      const cleanedCount = await this.deadLetterQueueService.cleanupExpiredDeadLetterMessages();
      this.logger.log(`🧹 Cleaned ${cleanedCount} expired dead letter messages`);

    } catch (error) {
      this.logger.error('❌ Failed to attempt dead letter recovery', error);
    }
  }

  /**
   * 发送告警
   */
  private async sendAlert(level: string, healthReport: any): Promise<void> {
    // 这里应该集成实际的告警系统（如邮件、短信、Slack等）
    this.logger.log(`📢 Sending ${level} alert:`, {
      level,
      issues: healthReport.issues,
      recommendations: healthReport.recommendations,
      timestamp: healthReport.timestamp,
    });

    // 记录告警到Redis
    const alertKey = `queue_alerts:${level}:${Date.now()}`;
    await this.redisService.set(
      alertKey,
      JSON.stringify({
        level,
        healthReport,
        sentAt: new Date(),
      }),
      3600 * 24 * 7, // 保存7天
      'global'
    );
  }

  /**
   * 记录监控结果
   */
  private async recordMonitoringResult(healthReport: any): Promise<void> {
    const resultKey = `queue_monitoring:${new Date().toISOString().split('T')[0]}`;
    
    try {
      // 获取今日监控记录
      const todayRecordsData = await this.redisService.get(resultKey, 'global');
      const todayRecords = todayRecordsData ? JSON.parse(todayRecordsData as string) : [];

      // 添加新记录
      todayRecords.push({
        timestamp: healthReport.timestamp,
        status: healthReport.overallStatus,
        issueCount: healthReport.issues.length,
        totalMessages: healthReport.metrics.totalMessages,
        deadLetterMessages: healthReport.metrics.deadLetterMessages,
      });

      // 限制记录数量（每天最多保存288条，即每5分钟一条）
      if (todayRecords.length > 288) {
        todayRecords.shift();
      }

      await this.redisService.set(resultKey, JSON.stringify(todayRecords), 3600 * 24 * 30, 'global');

    } catch (error) {
      this.logger.error('❌ Failed to record monitoring result', error);
    }
  }

  /**
   * 获取吞吐量数据
   */
  private async getThroughputData(): Promise<any> {
    // 这里应该从实际的统计数据中获取吞吐量
    // 暂时返回模拟数据
    return {
      messagesPerSecond: 10,
      messagesPerMinute: 600,
      messagesPerHour: 36000,
    };
  }

  /**
   * 获取延迟数据
   */
  private async getLatencyData(): Promise<any> {
    // 这里应该从实际的统计数据中获取延迟信息
    // 暂时返回模拟数据
    return {
      avgProcessingTime: 1000, // 1秒
      p95ProcessingTime: 2000, // 2秒
      p99ProcessingTime: 5000, // 5秒
    };
  }

  /**
   * 获取错误率数据
   */
  private async getErrorRateData(): Promise<any> {
    try {
      const deadLetterStats = await this.deadLetterQueueService.getDeadLetterStats();
      const queueStats = await this.queueManagerService.getQueueStats();

      const totalMessages = queueStats?.totalMessages || 0;
      const deadLetterMessages = deadLetterStats?.totalMessages || 0;
      const totalProcessed = totalMessages + deadLetterMessages;

      return {
        totalErrors: deadLetterMessages,
        errorRate: totalProcessed > 0 ? deadLetterMessages / totalProcessed : 0,
        deadLetterRate: totalProcessed > 0 ? deadLetterMessages / totalProcessed : 0,
      };

    } catch (error) {
      this.logger.error('❌ Failed to get error rate data', error);
      return {
        totalErrors: 0,
        errorRate: 0,
        deadLetterRate: 0,
      };
    }
  }
}
