import { Injectable, CanActivate, ExecutionContext, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';
import { WsException } from '@nestjs/websockets';
import { Socket } from 'socket.io';
import { ConfigService } from '@nestjs/config';

// 装饰器
import { TOKEN_SCOPE_KEY } from '../decorators/token-scope.decorator';

// 新增：Token类型定义
interface TokenPayload {
  sub: string;
  username: string;
  email: string;
  roles: string[];
  permissions: string[];
  sessionId: string;
  deviceId?: string;
  scope: 'account' | 'character';
  iat: number;
  exp: number;
  jti: string;
}

interface CharacterTokenPayload extends TokenPayload {
  characterId: string;
  serverId: string;
  scope: 'character';
}

/**
 * WebSocket 认证守卫
 *
 * 验证 WebSocket 连接的 JWT 令牌，确保只有经过认证的用户
 * 才能访问 WebSocket 端点
 */
@Injectable()
export class WsAuthGuard implements CanActivate {
  private readonly logger = new Logger(WsAuthGuard.name);

  constructor(
    private readonly jwtService: JwtService,
    private readonly reflector: Reflector,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 验证 WebSocket 连接的认证状态
   */
  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      const client: Socket = context.switchToWs().getClient();

      // 检查是否为公开事件
      const isPublic = this.reflector.getAllAndOverride<boolean>('isPublic', [
        context.getHandler(),
        context.getClass(),
      ]);

      if (isPublic) {
        return true;
      }

      const token = this.extractTokenFromSocket(client);

      if (!token) {
        this.logger.warn(`WebSocket authentication failed: No token provided`);
        throw new WsException('Authentication required');
      }

      // 验证双层Token
      const payload = await this.validateDualToken(token);

      // 将用户信息附加到 socket 对象
      client.data.user = {
        id: payload.sub,
        username: payload.username,
        email: payload.email,
        roles: payload.roles || [],
        permissions: payload.permissions || [],
        sessionId: payload.sessionId,
        deviceId: payload.deviceId,
        tokenScope: payload.scope,
      };

      // 如果是角色Token，附加角色上下文
      if (payload.scope === 'character') {
        const characterPayload = payload as CharacterTokenPayload;
        client.data.character = {
          characterId: characterPayload.characterId,
          serverId: characterPayload.serverId,
        };
      }

      // 检查Token作用域
      const requiredScope = this.reflector.get<'account' | 'character'>(
        TOKEN_SCOPE_KEY,
        context.getHandler()
      );

      if (requiredScope && payload.scope !== requiredScope) {
        throw new WsException(`Token作用域不匹配，期望: ${requiredScope}, 实际: ${payload.scope}`);
      }

      // 检查所需权限
      const requiredRoles = this.reflector.getAllAndOverride<string[]>('roles', [
        context.getHandler(),
        context.getClass(),
      ]);

      if (requiredRoles && requiredRoles.length > 0) {
        const userRoles = payload.roles || [];
        const hasRequiredRole = requiredRoles.some(role => userRoles.includes(role));

        if (!hasRequiredRole) {
          throw new WsException('Insufficient permissions');
        }
      }

      this.logger.debug(`WebSocket authentication successful for user: ${payload.sub}`);
      return true;

    } catch (error) {
      this.logger.error(`WebSocket authentication failed: ${error.message}`);

      if (error.name === 'JsonWebTokenError') {
        throw new WsException('Invalid token');
      } else if (error.name === 'TokenExpiredError') {
        throw new WsException('Token expired');
      } else if (error instanceof WsException) {
        throw error;
      } else {
        throw new WsException('Authentication failed');
      }
    }
  }

  /**
   * 从 Socket 连接中提取 JWT 令牌
   */
  private extractTokenFromSocket(client: Socket): string | null {
    // 方法1: 从 auth 对象中获取（推荐方式）
    const authToken = client.handshake.auth?.token;
    if (authToken && typeof authToken === 'string') {
      return authToken;
    }

    // 方法2: 从查询参数中获取
    const queryToken = client.handshake.query?.token;
    if (queryToken && typeof queryToken === 'string') {
      return queryToken;
    }

    // 方法3: 从认证头中获取
    const authHeader = client.handshake.headers?.authorization;
    if (authHeader && typeof authHeader === 'string') {
      const [type, token] = authHeader.split(' ');
      if (type === 'Bearer' && token) {
        return token;
      }
    }

    // 方法4: 从 Cookie 中获取
    const cookies = client.handshake.headers?.cookie;
    if (cookies) {
      const tokenMatch = cookies.match(/token=([^;]+)/);
      if (tokenMatch) {
        return tokenMatch[1];
      }
    }

    return null;
  }

  /**
   * 验证双层Token
   * 支持账号Token和角色Token的验证
   */
  private async validateDualToken(token: string): Promise<TokenPayload> {
    try {
      // 首先尝试解析Token获取基本信息（不验证签名）
      const decoded = this.jwtService.decode(token) as any;

      if (!decoded || !decoded.scope) {
        throw new WsException('Invalid token format');
      }

      // 根据scope字段选择验证方法
      switch (decoded.scope) {
        case 'account':
          return await this.validateAccountToken(token);
        case 'character':
          return await this.validateCharacterToken(token);
        default:
          throw new WsException(`Unsupported token scope: ${decoded.scope}`);
      }

    } catch (error) {
      if (error instanceof WsException) {
        throw error;
      }
      throw new WsException('Token validation failed');
    }
  }

  /**
   * 验证账号Token
   */
  private async validateAccountToken(token: string): Promise<TokenPayload> {
    const accountSecret = this.configService.get<string>('gateway.security.jwtSecret');

    // 调试日志：验证JWT密钥配置
    this.logger.debug(`🔑 JWT密钥配置: ${accountSecret ? '已配置' : '未配置'}`);
    this.logger.debug(`🔑 JWT密钥长度: ${accountSecret?.length || 0}`);

    const payload = this.jwtService.verify(token, { secret: accountSecret }) as TokenPayload;

    // 验证Token类型
    if (payload.scope !== 'account') {
      throw new WsException('Expected account token');
    }

    // 验证权限边界：账号Token不应包含角色信息
    if ((payload as any).characterId || (payload as any).serverId) {
      throw new WsException('Account token should not contain character information');
    }

    return payload;
  }

  /**
   * 验证角色Token
   */
  private async validateCharacterToken(token: string): Promise<CharacterTokenPayload> {
    const characterSecret = this.configService.get<string>('gateway.security.characterJwtSecret') ||
                           this.configService.get<string>('gateway.security.jwtSecret');

    const payload = this.jwtService.verify(token, { secret: characterSecret }) as CharacterTokenPayload;

    // 验证Token类型
    if (payload.scope !== 'character') {
      throw new WsException('Expected character token');
    }

    // 验证权限边界：角色Token必须包含角色和区服信息
    if (!payload.characterId || !payload.serverId) {
      throw new WsException('Character token must contain character and server information');
    }

    return payload;
  }
}
