import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

// 认证模块
import { GatewayAuthModule } from '../gateway-auth/gateway-auth.module';

// 监控模块
import { MonitoringModule } from '../monitoring/monitoring.module';

// 服务发现模块
import { DiscoveryModule } from '@gateway/infra/discovery/discovery.module';

// 控制器
import {InstanceManagementController} from "@gateway/modules/admin/controllers/instance-management.controller";
import {ServiceRegistryController} from "@gateway/modules/admin/controllers/service-registry.controller";

/**
 * 管理功能模块
 *
 * 职责：
 * - 提供服务注册和管理接口
 * - 管理服务实例生命周期
 * - 提供系统管理功能
 *
 * 依赖分析：
 * - ConfigModule：获取配置
 * - DiscoveryModule：服务发现
 * - GatewayAuthModule：管理员认证
 * - MonitoringModule：系统监控
 *
 * 注意：控制器文件需要从app/admin目录移动过来
 */
@Module({
  imports: [
    ConfigModule,
    DiscoveryModule,
    GatewayAuthModule,
    MonitoringModule,
  ],
  controllers: [
    ServiceRegistryController,
    InstanceManagementController,
  ],
  exports: [],
})
export class AdminModule {}
