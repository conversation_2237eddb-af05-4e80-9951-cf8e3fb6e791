import { Controller, Post, Get, Delete, Put, Body, Param, Query, UseGuards, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { 
  InstanceLifecycleService,
  ServerConfigGeneratorService,
  ContainerOrchestrationService,
  InstanceDeploymentConfig,
  DeploymentOptions,
  OrchestrationPlatform
} from '@libs/service-registry';

/**
 * 实例管理API控制器
 * 
 * 提供微服务实例的完整生命周期管理功能：
 * - 实例启动、停止、重启
 * - 配置生成和模板管理
 * - 容器编排集成
 * - 扩缩容和滚动更新
 * - 部署状态监控
 */
@ApiTags('Admin - Instance Management')
@Controller('api/admin/instance-management')
export class InstanceManagementController {
  private readonly logger = new Logger(InstanceManagementController.name);

  constructor(
    private readonly instanceLifecycle: InstanceLifecycleService,
    private readonly configGenerator: ServerConfigGeneratorService,
    private readonly orchestration: ContainerOrchestrationService,
  ) {}

  /**
   * 启动服务实例
   */
  @Post('instances/start')
  @ApiOperation({ summary: '启动服务实例' })
  @ApiResponse({ status: 201, description: '启动成功' })
  async startInstance(@Body() config: InstanceDeploymentConfig) {
    this.logger.log(`🚀 启动实例请求: ${config.serviceName}@${config.serverId}-${config.instanceIndex}`);
    
    try {
      const instanceId = await this.instanceLifecycle.startInstance(config);
      
      return {
        success: true,
        data: {
          instanceId,
          config,
          startedAt: new Date().toISOString(),
        },
        message: '实例启动成功',
      };
    } catch (error) {
      this.logger.error(`❌ 启动实例失败`, error);
      
      return {
        success: false,
        error: {
          code: 'INSTANCE_START_FAILED',
          message: error.message,
        },
      };
    }
  }

  /**
   * 停止服务实例
   */
  @Post('instances/:instanceId/stop')
  @ApiOperation({ summary: '停止服务实例' })
  @ApiResponse({ status: 200, description: '停止成功' })
  async stopInstance(
    @Param('instanceId') instanceId: string,
    @Body() body: { graceful?: boolean } = {}
  ) {
    this.logger.log(`🛑 停止实例请求: ${instanceId}, 优雅关闭=${body.graceful ?? true}`);
    
    try {
      await this.instanceLifecycle.stopInstance(instanceId, body.graceful ?? true);
      
      return {
        success: true,
        message: '实例停止成功',
      };
    } catch (error) {
      this.logger.error(`❌ 停止实例失败: ${instanceId}`, error);
      
      return {
        success: false,
        error: {
          code: 'INSTANCE_STOP_FAILED',
          message: error.message,
        },
      };
    }
  }

  /**
   * 重启服务实例
   */
  @Post('instances/:instanceId/restart')
  @ApiOperation({ summary: '重启服务实例' })
  @ApiResponse({ status: 200, description: '重启成功' })
  async restartInstance(@Param('instanceId') instanceId: string) {
    this.logger.log(`🔄 重启实例请求: ${instanceId}`);
    
    try {
      await this.instanceLifecycle.restartInstance(instanceId);
      
      return {
        success: true,
        message: '实例重启成功',
      };
    } catch (error) {
      this.logger.error(`❌ 重启实例失败: ${instanceId}`, error);
      
      return {
        success: false,
        error: {
          code: 'INSTANCE_RESTART_FAILED',
          message: error.message,
        },
      };
    }
  }

  /**
   * 获取实例状态
   */
  @Get('instances/:instanceId/status')
  @ApiOperation({ summary: '获取实例状态' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getInstanceStatus(@Param('instanceId') instanceId: string) {
    try {
      const status = this.instanceLifecycle.getInstanceStatus(instanceId);
      
      if (!status) {
        return {
          success: false,
          error: {
            code: 'INSTANCE_NOT_FOUND',
            message: '实例不存在',
          },
        };
      }
      
      return {
        success: true,
        data: status,
      };
    } catch (error) {
      this.logger.error(`❌ 获取实例状态失败: ${instanceId}`, error);
      
      return {
        success: false,
        error: {
          code: 'GET_STATUS_FAILED',
          message: error.message,
        },
      };
    }
  }

  /**
   * 获取所有实例状态
   */
  @Get('instances/status')
  @ApiOperation({ summary: '获取所有实例状态' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getAllInstanceStatuses() {
    try {
      const statuses = this.instanceLifecycle.getAllInstanceStatuses();
      
      return {
        success: true,
        data: {
          totalInstances: statuses.size,
          instances: Array.from(statuses.entries()).map(([id, status]) => ({
            instanceId: id,
            ...status,
          })),
        },
      };
    } catch (error) {
      this.logger.error(`❌ 获取所有实例状态失败`, error);
      
      return {
        success: false,
        error: {
          code: 'GET_ALL_STATUS_FAILED',
          message: error.message,
        },
      };
    }
  }

  /**
   * 扩容服务
   */
  @Post('services/:serviceName/:serverId/scale-up')
  @ApiOperation({ summary: '扩容服务' })
  @ApiResponse({ status: 200, description: '扩容成功' })
  async scaleUp(
    @Param('serviceName') serviceName: string,
    @Param('serverId') serverId: string,
    @Body() body: { targetInstances: number }
  ) {
    this.logger.log(`📈 扩容请求: ${serviceName}@${serverId} -> ${body.targetInstances}`);
    
    try {
      const newInstanceIds = await this.instanceLifecycle.scaleUp(
        serviceName,
        serverId,
        body.targetInstances
      );
      
      return {
        success: true,
        data: {
          serviceName,
          serverId,
          targetInstances: body.targetInstances,
          newInstances: newInstanceIds,
        },
        message: `扩容成功，新增 ${newInstanceIds.length} 个实例`,
      };
    } catch (error) {
      this.logger.error(`❌ 扩容失败: ${serviceName}@${serverId}`, error);
      
      return {
        success: false,
        error: {
          code: 'SCALE_UP_FAILED',
          message: error.message,
        },
      };
    }
  }

  /**
   * 缩容服务
   */
  @Post('services/:serviceName/:serverId/scale-down')
  @ApiOperation({ summary: '缩容服务' })
  @ApiResponse({ status: 200, description: '缩容成功' })
  async scaleDown(
    @Param('serviceName') serviceName: string,
    @Param('serverId') serverId: string,
    @Body() body: { targetInstances: number }
  ) {
    this.logger.log(`📉 缩容请求: ${serviceName}@${serverId} -> ${body.targetInstances}`);
    
    try {
      await this.instanceLifecycle.scaleDown(
        serviceName,
        serverId,
        body.targetInstances
      );
      
      return {
        success: true,
        data: {
          serviceName,
          serverId,
          targetInstances: body.targetInstances,
        },
        message: '缩容成功',
      };
    } catch (error) {
      this.logger.error(`❌ 缩容失败: ${serviceName}@${serverId}`, error);
      
      return {
        success: false,
        error: {
          code: 'SCALE_DOWN_FAILED',
          message: error.message,
        },
      };
    }
  }

  /**
   * 生成服务配置
   */
  @Post('config/generate')
  @ApiOperation({ summary: '生成服务配置' })
  @ApiResponse({ status: 200, description: '生成成功' })
  async generateConfig(@Body() body: {
    serviceName: string;
    serverId: string;
    instanceIndex?: number;
    environment?: 'development' | 'staging' | 'production';
  }) {
    try {
      const config = this.configGenerator.generateServiceConfig(
        body.serviceName,
        body.serverId,
        body.instanceIndex || 1,
        { environment: body.environment }
      );
      
      return {
        success: true,
        data: config,
      };
    } catch (error) {
      this.logger.error(`❌ 生成配置失败`, error);
      
      return {
        success: false,
        error: {
          code: 'CONFIG_GENERATION_FAILED',
          message: error.message,
        },
      };
    }
  }

  /**
   * 部署服务到容器编排平台
   */
  @Post('deployments')
  @ApiOperation({ summary: '部署服务' })
  @ApiResponse({ status: 201, description: '部署成功' })
  async deployServices(@Body() options: DeploymentOptions) {
    this.logger.log(`🚀 部署请求: 平台=${options.platform}, 服务数=${options.services.length}`);
    
    try {
      const deploymentId = await this.orchestration.deployServices(options);
      
      return {
        success: true,
        data: {
          deploymentId,
          platform: options.platform,
          services: options.services,
          createdAt: new Date().toISOString(),
        },
        message: '部署成功',
      };
    } catch (error) {
      this.logger.error(`❌ 部署失败`, error);
      
      return {
        success: false,
        error: {
          code: 'DEPLOYMENT_FAILED',
          message: error.message,
        },
      };
    }
  }

  /**
   * 停止部署
   */
  @Delete('deployments/:deploymentId')
  @ApiOperation({ summary: '停止部署' })
  @ApiResponse({ status: 200, description: '停止成功' })
  async stopDeployment(@Param('deploymentId') deploymentId: string) {
    this.logger.log(`🛑 停止部署请求: ${deploymentId}`);
    
    try {
      await this.orchestration.stopDeployment(deploymentId);
      
      return {
        success: true,
        message: '部署已停止',
      };
    } catch (error) {
      this.logger.error(`❌ 停止部署失败: ${deploymentId}`, error);
      
      return {
        success: false,
        error: {
          code: 'STOP_DEPLOYMENT_FAILED',
          message: error.message,
        },
      };
    }
  }

  /**
   * 获取部署状态
   */
  @Get('deployments/:deploymentId/status')
  @ApiOperation({ summary: '获取部署状态' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getDeploymentStatus(@Param('deploymentId') deploymentId: string) {
    try {
      const status = this.orchestration.getDeploymentStatus(deploymentId);
      
      if (!status) {
        return {
          success: false,
          error: {
            code: 'DEPLOYMENT_NOT_FOUND',
            message: '部署不存在',
          },
        };
      }
      
      return {
        success: true,
        data: status,
      };
    } catch (error) {
      this.logger.error(`❌ 获取部署状态失败: ${deploymentId}`, error);
      
      return {
        success: false,
        error: {
          code: 'GET_DEPLOYMENT_STATUS_FAILED',
          message: error.message,
        },
      };
    }
  }

  /**
   * 获取所有部署
   */
  @Get('deployments')
  @ApiOperation({ summary: '获取所有部署' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getAllDeployments() {
    try {
      const deployments = this.orchestration.getAllDeployments();
      
      return {
        success: true,
        data: {
          totalDeployments: deployments.length,
          deployments,
        },
      };
    } catch (error) {
      this.logger.error(`❌ 获取所有部署失败`, error);
      
      return {
        success: false,
        error: {
          code: 'GET_ALL_DEPLOYMENTS_FAILED',
          message: error.message,
        },
      };
    }
  }

  /**
   * 扩缩容部署中的服务
   */
  @Put('deployments/:deploymentId/services/:serviceName/:serverId/scale')
  @ApiOperation({ summary: '扩缩容部署中的服务' })
  @ApiResponse({ status: 200, description: '扩缩容成功' })
  async scaleDeploymentService(
    @Param('deploymentId') deploymentId: string,
    @Param('serviceName') serviceName: string,
    @Param('serverId') serverId: string,
    @Body() body: { targetInstances: number }
  ) {
    this.logger.log(`📊 部署扩缩容请求: ${deploymentId}/${serviceName}@${serverId} -> ${body.targetInstances}`);
    
    try {
      await this.orchestration.scaleService(
        deploymentId,
        serviceName,
        serverId,
        body.targetInstances
      );
      
      return {
        success: true,
        message: '扩缩容成功',
      };
    } catch (error) {
      this.logger.error(`❌ 部署扩缩容失败`, error);
      
      return {
        success: false,
        error: {
          code: 'DEPLOYMENT_SCALE_FAILED',
          message: error.message,
        },
      };
    }
  }
}
