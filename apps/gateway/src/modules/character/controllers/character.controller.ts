import { 
  Controller, 
  Post, 
  Get, 
  Body, 
  UseGuards, 
  HttpStatus,
  Logger,
  Query
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiBearerAuth,
  ApiBody,
  ApiQuery
} from '@nestjs/swagger';
// ❌ 删除：JwtAuthGuard已删除，使用AuthGuard替代
import { CurrentUser } from '@gateway/common/decorators/current-user.decorator';
import { User } from '../../../common/interfaces';
import { CharacterService } from '../services/character.service';
import { 
  CreateCharacterRequestDto,
  CharacterLoginRequestDto,
  GetCharacterListRequestDto,
  CharacterLoginResponseDto,
  CharacterListResponseDto,
  CreateCharacterResponseDto
} from '../dto/character.dto';

/**
 * 角色管理控制器
 * 
 * 提供角色相关的API接口：
 * - 获取角色列表
 * - 创建新角色
 * - 角色登录
 */
@ApiTags('角色管理')
@Controller('character')
@UseGuards(AuthGuard)
@ApiBearerAuth()
export class CharacterController {
  private readonly logger = new Logger(CharacterController.name);

  constructor(
    private readonly characterService: CharacterService,
  ) {}

  /**
   * 获取用户在指定区服的角色列表
   */
  @Get('list')
  @ApiOperation({ 
    summary: '获取角色列表',
    description: '获取当前用户在指定区服的所有角色信息'
  })
  @ApiQuery({ 
    name: 'serverId', 
    description: '区服ID',
    example: 'server001'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取角色列表成功',
    type: CharacterListResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: '未授权访问',
  })
  async getCharacterList(
    @CurrentUser() user: User,
    @Query('serverId') serverId: string,
  ): Promise<CharacterListResponseDto> {
    this.logger.log(`📋 [API] 获取角色列表: userId=${user.id}, serverId=${serverId}`);

    const request: GetCharacterListRequestDto = { serverId };
    return await this.characterService.getCharacterList(user.id, request);
  }

  /**
   * 创建新角色
   */
  @Post('create')
  @ApiOperation({ 
    summary: '创建角色',
    description: '在指定区服创建新的游戏角色'
  })
  @ApiBody({
    description: '创建角色请求参数',
    type: CreateCharacterRequestDto,
    examples: {
      example1: {
        summary: '创建角色示例',
        value: {
          serverId: 'server001',
          name: '传奇教练',
          faceIcon: 15,
          qualified: 4
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '角色创建成功',
    type: CreateCharacterResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: '请求参数错误或角色名称已存在',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: '角色名称已存在',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: '未授权访问',
  })
  async createCharacter(
    @CurrentUser() user: User,
    @Body() createCharacterDto: CreateCharacterRequestDto,
  ): Promise<CreateCharacterResponseDto> {
    this.logger.log(`🎭 [API] 创建角色: userId=${user.id}, serverId=${createCharacterDto.serverId}, name=${createCharacterDto.name}`);

    return await this.characterService.createCharacter(user.id, createCharacterDto);
  }

  /**
   * 角色登录
   */
  @Post('login')
  @ApiOperation({ 
    summary: '角色登录',
    description: '使用指定角色登录到游戏，获取角色Token'
  })
  @ApiBody({
    description: '角色登录请求参数',
    type: CharacterLoginRequestDto,
    examples: {
      example1: {
        summary: '角色登录示例',
        value: {
          serverId: 'server001',
          characterId: 'char_123456789'
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '角色登录成功',
    type: CharacterLoginResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: '角色不存在或不属于当前用户',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '角色不存在',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: '未授权访问',
  })
  async characterLogin(
    @CurrentUser() user: User,
    @Body() loginDto: CharacterLoginRequestDto,
  ): Promise<CharacterLoginResponseDto> {
    this.logger.log(`🚪 [API] 角色登录: userId=${user.id}, serverId=${loginDto.serverId}, characterId=${loginDto.characterId}`);

    return await this.characterService.characterLogin(user.id, loginDto);
  }

  /**
   * 验证角色Token（可选接口，用于调试）
   */
  @Post('validate-token')
  @ApiOperation({ 
    summary: '验证角色Token',
    description: '验证角色Token的有效性（调试用）'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Token验证成功',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Token无效或已过期',
  })
  async validateCharacterToken(
    @Body('characterToken') characterToken: string,
  ): Promise<{ valid: boolean; payload?: any; message: string }> {
    this.logger.log(`🔍 [API] 验证角色Token`);

    try {
      // 这里可以调用Auth服务验证Token
      // const payload = await this.authService.validateCharacterToken(characterToken);
      
      return {
        valid: true,
        message: 'Token验证成功',
        // payload,
      };
    } catch (error) {
      return {
        valid: false,
        message: 'Token无效或已过期',
      };
    }
  }
}
