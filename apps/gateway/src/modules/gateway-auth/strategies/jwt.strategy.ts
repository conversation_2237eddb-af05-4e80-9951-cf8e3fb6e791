import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ConfigService } from '@nestjs/config';
import { ExtractJwt, Strategy } from 'passport-jwt';

import { UserService, JwtPayload } from '../services/user.service';

/**
 * JWT 认证策略
 * 
 * 使用 Passport JWT 策略验证 JWT 令牌
 */
@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private readonly configService: ConfigService,
    private readonly userService: UserService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('gateway.security.jwtSecret'),
      issuer: configService.get<string>('gateway.security.jwtIssuer', 'football-manager-gateway'),
      audience: configService.get<string>('gateway.security.jwtAudience', 'football-manager-app'),
    });
  }

  /**
   * 验证 JWT 载荷
   */
  async validate(payload: JwtPayload) {
    try {
      // 验证用户是否仍然存在且有效
      const user = await this.userService.getCurrentUser(payload.sub);
      
      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      return {
        id: payload.sub,
        username: payload.username,
        email: payload.email,
        roles: payload.roles || [],
        permissions: payload.permissions || [],
      };
    } catch (error) {
      throw new UnauthorizedException('Invalid token');
    }
  }
}
