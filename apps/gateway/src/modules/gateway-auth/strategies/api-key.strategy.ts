import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-custom';
import { Request } from 'express';
import { RedisService } from '@common/redis';

/**
 * API Key 认证策略
 * 
 * 验证 API Key 的有效性，支持第三方服务集成
 */
@Injectable()
export class ApiKeyStrategy extends PassportStrategy(Strategy, 'api-key') {
  private readonly API_KEY_PREFIX = 'auth:apikey:';

  constructor(private readonly redisService: RedisService) {
    super();
  }

  /**
   * 验证 API Key
   */
  async validate(req: Request): Promise<any> {
    const apiKey = this.extractApiKey(req);
    
    if (!apiKey) {
      throw new UnauthorizedException('API Key required');
    }

    try {
      // 从 Redis 中验证 API Key
      const keyData = await this.redisService.get(`${this.API_KEY_PREFIX}${apiKey}`);
      
      if (!keyData) {
        throw new UnauthorizedException('Invalid API Key');
      }

      const keyInfo = JSON.parse(keyData as string);
      
      // 检查 API Key 是否过期
      if (keyInfo.expiresAt && new Date() > new Date(keyInfo.expiresAt)) {
        throw new UnauthorizedException('API Key expired');
      }

      // 检查 API Key 是否被禁用
      if (keyInfo.disabled) {
        throw new UnauthorizedException('API Key disabled');
      }

      // 更新最后使用时间
      await this.updateLastUsed(apiKey);

      return {
        id: keyInfo.userId || keyInfo.clientId,
        type: 'api-key',
        name: keyInfo.name,
        permissions: keyInfo.permissions || [],
        rateLimit: keyInfo.rateLimit,
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new UnauthorizedException('API Key validation failed');
    }
  }

  /**
   * 从请求中提取 API Key
   */
  private extractApiKey(req: Request): string | null {
    // 方法1: 从 X-API-Key 头中获取
    const headerKey = req.headers['x-api-key'] as string;
    if (headerKey) {
      return headerKey;
    }

    // 方法2: 从 Authorization 头中获取 (ApiKey scheme)
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('ApiKey ')) {
      return authHeader.substring(7);
    }

    // 方法3: 从查询参数中获取
    const queryKey = req.query.api_key as string;
    if (queryKey) {
      return queryKey;
    }

    return null;
  }

  /**
   * 更新 API Key 最后使用时间
   */
  private async updateLastUsed(apiKey: string): Promise<void> {
    try {
      const keyData = await this.redisService.get(`${this.API_KEY_PREFIX}${apiKey}`);
      if (keyData) {
        const keyInfo = JSON.parse(keyData as string);
        keyInfo.lastUsedAt = new Date().toISOString();
        keyInfo.usageCount = (keyInfo.usageCount || 0) + 1;
        
        await this.redisService.set(`${this.API_KEY_PREFIX}${apiKey}`, JSON.stringify(keyInfo));
      }
    } catch (error) {
      // 记录错误但不影响认证流程
      console.error('Failed to update API key last used time:', error);
    }
  }
}
