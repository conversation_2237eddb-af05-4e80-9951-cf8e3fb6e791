import { Injectable, NestMiddleware, UnauthorizedException, ForbiddenException } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { AuthService } from '../services/auth.service';
import { AuthContext } from '@gateway/common/interfaces/auth.interface';

export interface AuthenticatedRequest extends Request {
  auth?: AuthContext;
  user?: any;
}

@Injectable()
export class AuthMiddleware implements NestMiddleware {
  constructor(private readonly authService: AuthService) {}

  async use(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      const authContext = await this.extractAuthContext(req);
      
      if (authContext.authenticated) {
        req.auth = authContext;
        req.user = authContext.user;
        
        // 设置响应头
        res.setHeader('X-Auth-Method', authContext.authMethod);
        res.setHeader('X-User-ID', authContext.user?.id || '');
      }
      
      next();
    } catch (error) {
      if (error instanceof UnauthorizedException || error instanceof ForbiddenException) {
        res.status(error.getStatus()).json({
          statusCode: error.getStatus(),
          message: error.message,
          error: error.name,
          timestamp: new Date().toISOString(),
        });
      } else {
        next(error);
      }
    }
  }

  private async extractAuthContext(req: AuthenticatedRequest): Promise<AuthContext> {
    // 1. 尝试 JWT Token 认证
    const jwtToken = this.extractJwtToken(req);
    if (jwtToken) {
      try {
        // return await this.authService.validateJwtToken(jwtToken); // 临时注释
        throw new Error('JWT validation not implemented yet'); // 临时抛出错误
      } catch (error) {
        // JWT 验证失败，继续尝试其他认证方式
      }
    }

    // 2. 尝试 API Key 认证
    const apiKey = this.extractApiKey(req);
    if (apiKey) {
      try {
        const clientIp = this.getClientIp(req);
        return await this.authService.validateApiKey(apiKey, clientIp);
      } catch (error) {
        // API Key 验证失败，继续尝试其他认证方式
      }
    }

    // 3. 尝试 Session 认证
    const sessionId = this.extractSessionId(req);
    if (sessionId) {
      try {
        const clientIp = this.getClientIp(req);
        const userAgent = req.get('User-Agent') || '';
        return await this.authService.validateSession(sessionId, clientIp, userAgent);
      } catch (error) {
        // Session 验证失败
      }
    }

    // 4. 返回未认证的上下文
    return {
      authenticated: false,
      authMethod: 'none',
      permissions: [],
      roles: [],
      metadata: {},
    };
  }

  private extractJwtToken(req: Request): string | null {
    const authHeader = req.get('Authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }
    
    // 也可以从查询参数中获取
    const tokenFromQuery = req.query.token as string;
    if (tokenFromQuery) {
      return tokenFromQuery;
    }
    
    return null;
  }

  private extractApiKey(req: Request): string | null {
    // 从头部获取
    const apiKeyFromHeader = req.get('X-API-Key') || req.get('X-Api-Key');
    if (apiKeyFromHeader) {
      return apiKeyFromHeader;
    }
    
    // 从查询参数获取
    const apiKeyFromQuery = req.query.api_key as string || req.query.apikey as string;
    if (apiKeyFromQuery) {
      return apiKeyFromQuery;
    }
    
    return null;
  }

  private extractSessionId(req: Request): string | null {
    // 从 Cookie 中获取
    const sessionId = req.cookies?.sessionId || req.cookies?.session_id;
    if (sessionId) {
      return sessionId;
    }
    
    // 从头部获取
    const sessionFromHeader = req.get('X-Session-ID');
    if (sessionFromHeader) {
      return sessionFromHeader;
    }
    
    return null;
  }

  private getClientIp(req: Request): string {
    return (
      req.get('X-Forwarded-For')?.split(',')[0]?.trim() ||
      req.get('X-Real-IP') ||
      req.get('X-Client-IP') ||
      req.connection.remoteAddress ||
      req.socket.remoteAddress ||
      '127.0.0.1'
    );
  }
}

@Injectable()
export class RequireAuthMiddleware implements NestMiddleware {
  constructor(private readonly authService: AuthService) {}

  async use(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    if (!req.auth?.authenticated) {
      return res.status(401).json({
        statusCode: 401,
        message: 'Authentication required',
        error: 'Unauthorized',
        timestamp: new Date().toISOString(),
      });
    }
    
    next();
  }
}

@Injectable()
export class RequireRoleMiddleware implements NestMiddleware {
  constructor(
    private readonly authService: AuthService,
    private readonly requiredRoles: string[],
  ) {}

  async use(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    if (!req.auth?.authenticated) {
      return res.status(401).json({
        statusCode: 401,
        message: 'Authentication required',
        error: 'Unauthorized',
        timestamp: new Date().toISOString(),
      });
    }

    const hasRole = this.authService.checkRole(req.auth, this.requiredRoles);
    if (!hasRole) {
      return res.status(403).json({
        statusCode: 403,
        message: `Required roles: ${this.requiredRoles.join(', ')}`,
        error: 'Forbidden',
        timestamp: new Date().toISOString(),
      });
    }
    
    next();
  }
}

@Injectable()
export class RequirePermissionMiddleware implements NestMiddleware {
  constructor(
    private readonly authService: AuthService,
    private readonly resource: string,
    private readonly action: string,
  ) {}

  async use(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    if (!req.auth?.authenticated) {
      return res.status(401).json({
        statusCode: 401,
        message: 'Authentication required',
        error: 'Unauthorized',
        timestamp: new Date().toISOString(),
      });
    }

    const hasPermission = await this.authService.checkPermission(
      req.auth,
      this.resource,
      this.action,
      { request: req },
    );

    if (!hasPermission) {
      return res.status(403).json({
        statusCode: 403,
        message: `Permission required: ${this.resource}:${this.action}`,
        error: 'Forbidden',
        timestamp: new Date().toISOString(),
      });
    }
    
    next();
  }
}
