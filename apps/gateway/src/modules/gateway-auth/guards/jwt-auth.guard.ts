import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { AuthService } from '../services/auth.service';

/**
 * 临时JWT认证守卫
 * 
 * 这是一个简化的守卫，用于替代被删除的复杂JWT守卫
 * 实际的JWT验证逻辑已经迁移到Auth服务
 */
@Injectable()
export class JwtAuthGuard implements CanActivate {
  constructor(private readonly authService: AuthService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = this.authService.extractTokenFromRequest(request);

    if (!token) {
      throw new UnauthorizedException('Authentication token is required');
    }

    try {
      // 使用AuthService的代理验证方法
      const result = await this.authService.validateToken(token);
      if (result.valid) {
        this.authService.injectUserContext(request, result);
        return true;
      }
      throw new UnauthorizedException(result.error || 'Invalid token');
    } catch (error) {
      throw new UnauthorizedException('Invalid authentication token');
    }
  }
}
