import { Injectable, UnauthorizedException, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { MicroserviceClientService } from '@common/microservice-kit';

import { LoginDto } from '../dto/login.dto';
import { ChangePasswordDto } from '../dto/change-password.dto';
import { MICROSERVICE_NAMES } from '@shared/constants';

/**
 * 认证服务接口
 */
export interface JwtPayload {
  sub: string;
  username: string;
  email: string;
  roles: string[];
  permissions: string[];
  iat?: number;
  exp?: number;
}

/**
 * 登录响应接口
 */
export interface LoginResponse {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  user: {
    id: string;
    username: string;
    email: string;
    roles: string[];
  };
}

/**
 * 用户服务
 *
 * 处理用户相关的业务逻辑，包括：
 * - 用户登录验证
 * - 用户注册流程
 * - 密码管理（修改、重置）
 * - 令牌刷新和会话管理
 * - 用户信息管理
 * - 用户状态管理
 *
 * 注意：此服务专注于用户业务流程，实际的令牌验证由核心层的 AuthService 处理
 */
@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly microserviceClient: MicroserviceClientService,
  ) {}

  /**
   * 用户登录 - 使用RPC调用认证服务
   */
  async login(loginDto: LoginDto): Promise<LoginResponse> {
    try {
      // 使用统一的微服务客户端调用认证服务，转换数据格式
      const authLoginData = {
        identifier: loginDto.username,  // 转换为Auth服务期望的格式
        password: loginDto.password,
        deviceInfo: {
          type: 'web',
          name: 'Gateway Client',
          fingerprint: 'gateway_client',
          userAgent: 'Gateway/1.0',
          ipAddress: '127.0.0.1'
        }
      };

      const authResult = await this.microserviceClient.call(
        MICROSERVICE_NAMES.AUTH_SERVICE,
        'auth.login',
        authLoginData
      );

      if (!authResult.success) {
        throw new UnauthorizedException('Invalid credentials');
      }

      return authResult;
    } catch (error) {
      this.logger.error(`Login failed: ${error.message}`);
      throw new UnauthorizedException('Authentication failed');
    }
  }


  /**
   * 刷新访问令牌 - 代理到Auth服务
   */
  async refreshToken(refreshToken: string): Promise<Omit<LoginResponse, 'user'>> {
    try {
      // 直接调用Auth服务的refreshToken功能
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.AUTH_SERVICE,
        'auth.refreshToken',
        { refreshToken }
      );

      if (!result.success) {
        throw new UnauthorizedException(result.error || 'Token refresh failed');
      }

      return result.data;
    } catch (error) {
      this.logger.error(`Token refresh failed: ${error.message}`);
      throw new UnauthorizedException('Token refresh failed');
    }
  }



  /**
   * 用户登出 - 代理到Auth服务
   */
  async logout(userId: string, token: string): Promise<void> {
    try {
      // 直接调用Auth服务的logout功能
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.AUTH_SERVICE,
        'auth.logout',
        { userId, token }
      );

      if (!result.success) {
        throw new Error(result.error || 'Logout failed');
      }

      this.logger.log(`用户 ${userId} 登出成功`);
    } catch (error) {
      this.logger.error(`用户 ${userId} 登出失败: ${error.message}`);
      throw error;
    }
  }



  /**
   * 获取当前用户信息 - 代理到Auth服务
   */
  async getCurrentUser(userId: string): Promise<any> {
    try {
      // 调用Auth服务的getUserProfile MessagePattern
      const userResult = await this.microserviceClient.call(
        MICROSERVICE_NAMES.AUTH_SERVICE,
        'user.getUserProfile',
        { userId }
      );

      if (!userResult.success) {
        throw new UnauthorizedException(userResult.error || 'User not found');
      }

      this.logger.log(`获取用户 ${userId} 信息成功`);
      return userResult.data;
    } catch (error) {
      this.logger.error(`获取用户信息失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 修改密码 - 代理到Auth服务
   */
  async changePassword(userId: string, changePasswordDto: ChangePasswordDto): Promise<void> {
    try {
      // 调用Auth服务的changePassword MessagePattern
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.AUTH_SERVICE,
        'user.changePassword',
        {
          userId,
          ...changePasswordDto,
        }
      );

      if (!result.success) {
        throw new UnauthorizedException(result.error || 'Password change failed');
      }

      this.logger.log(`用户 ${userId} 密码修改成功`);
    } catch (error) {
      this.logger.error(`密码修改失败: ${error.message}`);
      throw error;
    }
  }


}
