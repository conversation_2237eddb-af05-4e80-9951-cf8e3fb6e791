import { Injectable, UnauthorizedException, Logger } from '@nestjs/common';
// ❌ 删除：JwtService依赖，Gateway不应该直接处理JWT
import { ConfigService } from '@nestjs/config';
// ❌ 删除：RedisService依赖，Gateway不应该直接管理Token存储
import { MicroserviceClientService } from '@common/microservice-kit';

import { LoginDto } from '../dto/login.dto';
import { ChangePasswordDto } from '../dto/change-password.dto';
import {MICROSERVICE_NAMES} from "@libs/shared";

/**
 * 认证服务接口
 */
export interface JwtPayload {
  sub: string;
  username: string;
  email: string;
  roles: string[];
  permissions: string[];
  iat?: number;
  exp?: number;
}

/**
 * 登录响应接口
 */
export interface LoginResponse {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  user: {
    id: string;
    username: string;
    email: string;
    roles: string[];
  };
}

/**
 * 用户服务
 *
 * 处理用户相关的业务逻辑，包括：
 * - 用户登录验证
 * - 用户注册流程
 * - 密码管理（修改、重置）
 * - 令牌刷新和会话管理
 * - 用户信息管理
 * - 用户状态管理
 *
 * 注意：此服务专注于用户业务流程，实际的令牌验证由核心层的 AuthService 处理
 */
@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);
  // ❌ 删除：Token和黑名单管理常量，这些应该由Auth服务管理

  constructor(
    // ❌ 删除：JwtService, RedisService依赖，Gateway不应该直接处理JWT和存储
    private readonly configService: ConfigService,
    private readonly microserviceClient: MicroserviceClientService,
  ) {}

  /**
   * 用户登录 - 使用RPC调用认证服务
   */
  async login(loginDto: LoginDto): Promise<LoginResponse> {
    try {
      // 使用统一的微服务客户端调用认证服务
      const authResult = await this.microserviceClient.call(
        MICROSERVICE_NAMES.AUTH_SERVICE,
        'auth.login',
        loginDto
      );

      if (!authResult.success) {
        throw new UnauthorizedException('Invalid credentials');
      }

      // ✅ 直接返回Auth服务的登录结果，不在Gateway层生成Token
      return authResult;
    } catch (error) {
      this.logger.error(`Login failed: ${error.message}`);
      throw new UnauthorizedException('Authentication failed');
    }
  }

  // ❌ 删除：Token生成应该由Auth服务负责，Gateway不应该生成Token

  /**
   * 刷新访问令牌 - 代理到Auth服务
   */
  async refreshToken(refreshToken: string): Promise<Omit<LoginResponse, 'user'>> {
    try {
      // ✅ 直接调用Auth服务的refreshToken功能
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.AUTH_SERVICE,
        'refreshToken',
        { refreshToken }
      );

      if (!result.success) {
        throw new UnauthorizedException(result.error || 'Token refresh failed');
      }

      return result.data;
    } catch (error) {
      this.logger.error(`Token refresh failed: ${error.message}`);
      throw new UnauthorizedException('Token refresh failed');
    }
  }

  // ❌ 删除：Token生成应该由Auth服务负责

  /**
   * 用户登出
   */
  async logout(userId: string, token: string): Promise<void> {
    try {
      // 将令牌加入黑名单
      await this.addTokenToBlacklist(token);

      // 删除用户的所有刷新令牌
      await this.removeAllRefreshTokens(userId);

      this.logger.log(`User ${userId} logged out successfully`);
    } catch (error) {
      this.logger.error(`Logout failed for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  // ❌ 删除：Token验证应该由Auth服务负责

  /**
   * 获取当前用户信息 - 使用RPC调用
   */
  async getCurrentUser(userId: string): Promise<any> {
    try {
      // 使用统一的微服务客户端
      const userResult = await this.microserviceClient.call(
        'auth',
        'user.findById',
        { id: userId }
      );

      if (!userResult.success) {
        throw new UnauthorizedException('User not found');
      }

      return userResult.user;
    } catch (error) {
      this.logger.error(`Get current user failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * 修改密码 - 使用RPC调用
   */
  async changePassword(userId: string, changePasswordDto: ChangePasswordDto): Promise<void> {
    try {
      // 使用统一的微服务客户端
      const result = await this.microserviceClient.call(
        'auth',
        'auth.changePassword',
        {
          userId,
          ...changePasswordDto,
        }
      );

      if (!result.success) {
        throw new UnauthorizedException('Password change failed');
      }

      this.logger.log(`Password changed for user: ${userId}`);
    } catch (error) {
      this.logger.error(`Change password failed: ${error.message}`);
      throw error;
    }
  }

  // ==================== 私有方法 ====================

  // ❌ 删除：所有Token管理、黑名单管理、会话管理功能都应该由Auth服务负责
}
