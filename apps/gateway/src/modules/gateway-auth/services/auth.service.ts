import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '@common/redis';
import { DataType } from '@common/redis/types/redis.types';
import { MicroserviceClientService } from '@common/microservice-kit';
import { MICROSERVICE_NAMES } from '@libs/shared';
import * as crypto from 'crypto';

// 验证结果接口
interface ValidationResult {
  valid: boolean;
  user?: {
    id: string;
    username: string;
    email: string;
    roles: string[];
    permissions: string[];
    level?: number;
  };
  scope?: string[];
  error?: string;
}

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly microserviceClient: MicroserviceClientService,
  ) {}

  /**
   * 代理验证Token - 带缓存机制
   */
  async validateToken(token: string): Promise<ValidationResult> {
    if (!token) {
      return { valid: false, error: 'Token不能为空' };
    }

    // 1. 检查缓存
    const cacheKey = `token_validation:${this.hashToken(token)}`;
    const cached = await this.redisService.get(cacheKey, 'global');
    if (cached && typeof cached === 'string') {
      this.logger.debug('Token验证缓存命中');
      return JSON.parse(cached);
    }

    try {
      // 2. 调用Auth服务的@MessagePattern方法
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.AUTH_SERVICE,
        'verifyToken',  // 使用正确的MessagePattern
        { token }
      );

      // 3. 缓存验证结果（5分钟）
      await this.redisService.set(cacheKey, JSON.stringify(result), 300, 'global' );

      this.logger.debug('Token验证完成并缓存');
      return result;
    } catch (error) {
      this.logger.error('Token验证失败', error);
      return { valid: false, error: 'Token验证失败' };
    }
  }

  /**
   * 用户信息注入到请求上下文
   */
  injectUserContext(request: any, result: ValidationResult): void {
    if (result.valid && result.user) {
      request.user = {
        id: result.user.id,
        username: result.user.username,
        email: result.user.email,
        roles: result.user.roles,
        permissions: result.user.permissions,
        level: result.user.level,
        tokenScope: result.scope,
      };
    }
  }

  /**
   * 从请求中提取Token
   */
  extractTokenFromRequest(request: any): string | null {
    const authHeader = request.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }
    return null;
  }

  /**
   * Token哈希（用于缓存键）
   */
  private hashToken(token: string): string {
    return crypto.createHash('sha256').update(token).digest('hex').substring(0, 16);
  }
}
