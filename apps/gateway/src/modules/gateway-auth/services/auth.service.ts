import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '@common/redis';
import { DataType } from '@common/redis/types/redis.types';
import { MicroserviceClientService } from '@common/microservice-kit';
import { MICROSERVICE_NAMES } from '@libs/shared';
import * as crypto from 'crypto';

// 验证结果接口
interface ValidationResult {
  valid: boolean;
  user?: {
    id: string;
    username: string;
    email: string;
    roles: string[];
    permissions: string[];
    level?: number;
  };
  scope?: string[];
  error?: string;
}

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly microserviceClient: MicroserviceClientService,
  ) {}

  /**
   * 代理验证Token - 带缓存机制
   */
  async validateToken(token: string): Promise<ValidationResult> {
    if (!token) {
      return { valid: false, error: 'Token不能为空' };
    }

    // 1. 检查缓存
    const cacheKey = `token_validation:${this.hashToken(token)}`;
    const cached = await this.redisService.get(cacheKey, 'global');
    if (cached && typeof cached === 'string') {
      this.logger.debug('Token验证缓存命中');
      return JSON.parse(cached);
    }

    try {
      // 2. 调用Auth服务的@MessagePattern方法
      this.logger.debug(`调用Auth服务验证Token: ${token.substring(0, 8)}***`);
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.AUTH_SERVICE,
        'verifyToken',  // 使用正确的MessagePattern
        { token }
      );

      if (!result.success) {
        this.logger.warn(`Token验证失败: ${result.error}`);
        return { valid: false, error: result.error || 'Token验证失败' };
      }

      // 3. 缓存验证结果（5分钟）
      await this.redisService.set(cacheKey, JSON.stringify(result.data), 300, 'global');

      this.logger.debug('Token验证成功并缓存');
      return result.data;
    } catch (error) {
      this.logger.error(`Token验证异常: ${error.message}`, error.stack);
      return { valid: false, error: 'Token验证服务异常' };
    }
  }

  /**
   * 用户信息注入到请求上下文
   */
  injectUserContext(request: any, result: ValidationResult): void {
    if (result.valid && result.user) {
      request.user = {
        id: result.user.id,
        username: result.user.username,
        email: result.user.email,
        roles: result.user.roles,
        permissions: result.user.permissions,
        level: result.user.level,
        tokenScope: result.scope,
      };
    }
  }

  /**
   * 从请求中提取Token
   */
  extractTokenFromRequest(request: any): string | null {
    const authHeader = request.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }
    return null;
  }

  /**
   * 代理权限检查 - 调用Auth服务
   */
  async checkPermission(userId: string, resource: string, action: string): Promise<boolean> {
    try {
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.AUTH_SERVICE,
        'checkPermission',
        { userId, resource, action }
      );

      return result.success && result.data?.hasPermission;
    } catch (error) {
      this.logger.error('权限检查失败', error);
      return false;
    }
  }

  /**
   * 代理API Key验证 - 调用Auth服务的ApiKey模块
   */
  async validateApiKey(apiKey: string, clientIp?: string, userAgent?: string): Promise<ValidationResult> {
    try {
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.AUTH_SERVICE,
        'validateApiKey',
        { apiKey, clientIp, userAgent }
      );

      if (result.success) {
        return {
          valid: true,
          user: {
            id: result.data.id,
            username: result.data.name,
            email: '',
            roles: [],
            permissions: result.data.permissions || [],
          },
          scope: result.data.permissions || []
        };
      }

      return { valid: false, error: result.error || 'API Key验证失败' };
    } catch (error) {
      this.logger.error('API Key验证失败', error);
      return { valid: false, error: 'API Key验证失败' };
    }
  }

  /**
   * 代理用户登出 - 调用Auth服务
   */
  async logout(userId: string, token: string): Promise<{ success: boolean; error?: string }> {
    try {
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.AUTH_SERVICE,
        'logout',
        { userId, token }
      );

      // 清除本地缓存
      const cacheKey = `token_validation:${this.hashToken(token)}`;
      await this.redisService.del(cacheKey, 'global');

      return { success: result.success, error: result.error };
    } catch (error) {
      this.logger.error('登出失败', error);
      return { success: false, error: '登出失败' };
    }
  }

  /**
   * 代理刷新Token - 调用Auth服务
   */
  async refreshToken(refreshToken: string): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.AUTH_SERVICE,
        'refreshToken',
        { refreshToken }
      );

      return result;
    } catch (error) {
      this.logger.error('Token刷新失败', error);
      return { success: false, error: 'Token刷新失败' };
    }
  }

  /**
   * Token哈希（用于缓存键）
   */
  private hashToken(token: string): string {
    return crypto.createHash('sha256').update(token).digest('hex').substring(0, 16);
  }
}
