import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';

// 导入服务发现服务
import { ServiceDiscoveryService } from './service-discovery.service';

// 导入依赖模块
import { LoadBalancingModule } from '../../modules/load-balancing/load-balancing.module';

/**
 * 服务发现模块
 * 
 * 提供网关服务发现功能，包括：
 * - 服务注册和注销
 * - 服务实例发现
 * - 健康检查和监控
 * - 负载均衡支持
 * 
 * 职责范围：
 * - 自动发现可用的后端服务实例
 * - 维护服务实例的注册表
 * - 监控服务实例的健康状态
 * - 为负载均衡提供服务实例信息
 */
@Module({
  imports: [
    ConfigModule,
    EventEmitterModule.forRoot(),
    LoadBalancingModule,
  ],
  providers: [
    ServiceDiscoveryService,
  ],
  exports: [
    ServiceDiscoveryService,
  ],
})
export class DiscoveryModule {}
