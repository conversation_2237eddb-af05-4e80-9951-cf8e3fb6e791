import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';



/**
 * JWT 共享模块
 *
 * 统一管理 JWT 配置，支持双层Token机制：
 * - 账号Token：用于账号管理和区服选择
 * - 角色Token：用于游戏业务功能
 *
 * 解决的问题：
 * - 消除重复配置（原来在 AppModule、AuthModule、WebSocketModule 中重复配置）
 * - 统一配置键（解决 WebSocketModule 中使用不同配置键的问题）
 * - 确保配置一致性
 * - 支持双层Token验证和路由
 *
 * 配置说明：
 * - secret: 使用统一的配置键 'gateway.security.jwtSecret'
 * - characterSecret: 角色Token专用密钥
 * - expiresIn: JWT 令牌过期时间
 * - issuer: 令牌签发者
 * - audience: 令牌受众
 */
@Module({
  imports: [
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('gateway.security.jwtSecret'),
        signOptions: {
          expiresIn: configService.get<string>('gateway.security.jwtExpiresIn', '24h'),
          issuer: configService.get<string>('gateway.security.jwtIssuer', 'football-manager-gateway'),
          audience: configService.get<string>('gateway.security.jwtAudience', 'football-manager-app'),
        },
      }),
    }),
  ],
  providers: [
    
  ],
  exports: [
    JwtModule,
    
  ],
})
export class JwtSharedModule {}
