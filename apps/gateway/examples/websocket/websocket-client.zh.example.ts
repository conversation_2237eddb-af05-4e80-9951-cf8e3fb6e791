/**
 * WebSocket 客户端示例
 * 
 * 此示例演示如何从客户端应用程序连接到 WebSocket 网关并与之交互。
 */

import { io, Socket } from 'socket.io-client';

/**
 * 示例 1: 基础 WebSocket 连接
 */
export class BasicWebSocketExample {
  private socket: Socket | null = null;
  
  async connect(url: string = 'http://localhost:3000', token?: string) {
    console.log('=== 基础 WebSocket 连接示例 ===');
    
    const options: any = {
      transports: ['websocket', 'polling'], // 传输方式：WebSocket、轮询
      timeout: 5000, // 超时时间：5秒
    };
    
    // 如果提供了令牌，添加认证
    if (token) {
      options.auth = { token };
    }
    
    this.socket = io(url, options);
    
    // 连接事件
    this.socket.on('connect', () => {
      console.log('✓ 已连接到 WebSocket 服务器');
      console.log('Socket ID:', this.socket?.id);
    });
    
    this.socket.on('disconnect', (reason) => {
      console.log('✗ 与 WebSocket 服务器断开连接');
      console.log('原因:', reason);
    });
    
    this.socket.on('connect_error', (error) => {
      console.error('连接错误:', error.message);
    });
    
    // 服务器确认
    this.socket.on('connected', (data) => {
      console.log('收到服务器确认:', data);
    });
    
    return new Promise<void>((resolve, reject) => {
      this.socket?.on('connect', () => resolve());
      this.socket?.on('connect_error', (error) => reject(error));
    });
  }
  
  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }
  
  getSocket(): Socket | null {
    return this.socket;
  }
}

/**
 * 示例 2: 房间管理
 */
export class RoomManagementExample {
  constructor(private socket: Socket) {}
  
  async joinRoom(roomId: string, password?: string) {
    console.log('\n=== 房间管理示例 ===');
    console.log(`尝试加入房间: ${roomId}`);
    
    return new Promise<void>((resolve, reject) => {
      // 监听房间事件
      this.socket.on('joined_room', (data) => {
        console.log('✓ 成功加入房间:', data);
        resolve();
      });
      
      this.socket.on('error', (error) => {
        console.error('✗ 加入房间失败:', error.message);
        reject(new Error(error.message));
      });
      
      // 发送加入请求
      this.socket.emit('join_room', { roomId, password });
    });
  }
  
  async leaveRoom(roomId: string) {
    console.log(`离开房间: ${roomId}`);
    
    return new Promise<void>((resolve) => {
      this.socket.on('left_room', (data) => {
        console.log('✓ 成功离开房间:', data);
        resolve();
      });
      
      this.socket.emit('leave_room', { roomId });
    });
  }
  
  onUserJoined(callback: (data: any) => void) {
    this.socket.on('user_joined', callback);
  }
  
  onUserLeft(callback: (data: any) => void) {
    this.socket.on('user_left', callback);
  }
}

/**
 * 示例 3: 消息传递
 */
export class MessagingExample {
  constructor(private socket: Socket) {}
  
  sendRoomMessage(roomId: string, message: string, type: string = 'text') {
    console.log('\n=== 消息传递示例 ===');
    console.log(`向房间 ${roomId} 发送消息: "${message}"`);
    
    this.socket.emit('send_message', {
      roomId,
      message,
      type,
    });
  }
  
  sendPrivateMessage(targetUserId: string, message: string, type: string = 'text') {
    console.log(`向用户 ${targetUserId} 发送私信: "${message}"`);
    
    this.socket.emit('send_message', {
      targetUserId,
      message,
      type,
    });
  }
  
  onMessage(callback: (data: any) => void) {
    this.socket.on('message', (data) => {
      console.log('📨 收到消息:', data);
      callback(data);
    });
  }
  
  onPrivateMessage(callback: (data: any) => void) {
    this.socket.on('private_message', (data) => {
      console.log('📩 收到私信:', data);
      callback(data);
    });
  }
  
  onMessageSent(callback: (data: any) => void) {
    this.socket.on('message_sent', (data) => {
      console.log('✓ 消息发送确认:', data);
      callback(data);
    });
  }
  
  onOfflineMessages(callback: (data: any) => void) {
    this.socket.on('offline_messages', (data) => {
      console.log('📬 收到离线消息:', data);
      callback(data);
    });
  }
}

/**
 * 示例 4: 心跳和连接管理
 */
export class HeartbeatExample {
  private heartbeatInterval: NodeJS.Timeout | null = null;
  
  constructor(private socket: Socket) {}
  
  startHeartbeat(interval: number = 30000) {
    console.log('\n=== 心跳示例 ===');
    console.log(`启动心跳，间隔 ${interval}ms`);
    
    this.socket.on('heartbeat_ack', (data) => {
      console.log('💓 心跳确认:', data);
    });
    
    this.heartbeatInterval = setInterval(() => {
      console.log('💓 发送心跳...');
      this.socket.emit('heartbeat');
    }, interval);
  }
  
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
      console.log('💓 心跳已停止');
    }
  }
}

/**
 * 示例 5: 游戏特定事件
 */
export class GameEventsExample {
  constructor(private socket: Socket) {}
  
  setupGameEventListeners() {
    console.log('\n=== 游戏事件示例 ===');
    
    // 用户状态事件
    this.socket.on('user_status', (data) => {
      console.log('👤 用户状态更新:', data);
    });
    
    // 比赛事件
    this.socket.on('match_started', (data) => {
      console.log('⚽ 比赛开始:', data);
    });
    
    this.socket.on('match_ended', (data) => {
      console.log('🏁 比赛结束:', data);
    });
    
    this.socket.on('goal_scored', (data) => {
      console.log('⚽ 进球了:', data);
    });
    
    // 球员事件
    this.socket.on('player_transferred', (data) => {
      console.log('🔄 球员转会:', data);
    });
    
    this.socket.on('player_injured', (data) => {
      console.log('🏥 球员受伤:', data);
    });
    
    // 俱乐部事件
    this.socket.on('club_updated', (data) => {
      console.log('🏟️ 俱乐部更新:', data);
    });
    
    // 通知事件
    this.socket.on('notification', (data) => {
      console.log('🔔 通知:', data);
    });
  }
  
  // 发送游戏动作
  sendGameAction(action: string, data: any) {
    console.log(`🎮 发送游戏动作: ${action}`, data);
    this.socket.emit('game_action', { action, data });
  }
}

/**
 * 完整的 WebSocket 客户端示例
 */
export class CompleteWebSocketClient {
  private basicExample: BasicWebSocketExample;
  private roomExample: RoomManagementExample | null = null;
  private messagingExample: MessagingExample | null = null;
  private heartbeatExample: HeartbeatExample | null = null;
  private gameExample: GameEventsExample | null = null;
  
  constructor() {
    this.basicExample = new BasicWebSocketExample();
  }
  
  async connect(url?: string, token?: string) {
    await this.basicExample.connect(url, token);
    
    const socket = this.basicExample.getSocket();
    if (socket) {
      this.roomExample = new RoomManagementExample(socket);
      this.messagingExample = new MessagingExample(socket);
      this.heartbeatExample = new HeartbeatExample(socket);
      this.gameExample = new GameEventsExample(socket);
      
      // 设置游戏事件监听器
      this.gameExample.setupGameEventListeners();
    }
  }
  
  async runCompleteExample() {
    try {
      console.log('WebSocket 客户端完整示例');
      console.log('=========================');
      
      // 使用认证连接
      await this.connect('http://localhost:3000', 'your-jwt-token-here');
      
      // 启动心跳
      this.heartbeatExample?.startHeartbeat();
      
      // 加入房间
      await this.roomExample?.joinRoom('match_123');
      
      // 设置消息监听器
      this.messagingExample?.onMessage((data) => {
        console.log('收到房间消息:', data);
      });
      
      this.messagingExample?.onPrivateMessage((data) => {
        console.log('收到私信:', data);
      });
      
      // 发送一些消息
      this.messagingExample?.sendRoomMessage('match_123', '大家好！');
      
      // 等待一会儿
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 发送游戏动作
      this.gameExample?.sendGameAction('substitute_player', {
        matchId: 'match_123',
        playerOut: 'player_456',
        playerIn: 'player_789',
      });
      
      // 再等待一会儿
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 离开房间并断开连接
      await this.roomExample?.leaveRoom('match_123');
      this.heartbeatExample?.stopHeartbeat();
      this.basicExample.disconnect();
      
      console.log('\n✓ 完整的 WebSocket 示例成功完成！');
      
    } catch (error) {
      console.error('WebSocket 示例失败:', error);
    }
  }
}

/**
 * 使用示例
 */
export async function runWebSocketExamples() {
  console.log('WebSocket 客户端示例');
  console.log('===================');
  
  // 示例 1: 基础连接
  const basicExample = new BasicWebSocketExample();
  try {
    await basicExample.connect();
    console.log('基础连接成功');
    basicExample.disconnect();
  } catch (error) {
    console.error('基础连接失败:', error);
  }
  
  // 示例 2: 完整客户端
  const completeClient = new CompleteWebSocketClient();
  await completeClient.runCompleteExample();
}

// 导出所有示例
export {
  BasicWebSocketExample,
  RoomManagementExample,
  MessagingExample,
  HeartbeatExample,
  GameEventsExample,
  CompleteWebSocketClient,
};
