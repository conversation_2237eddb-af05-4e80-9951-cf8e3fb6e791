import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { RedisService } from '@common/redis';
import { DataType } from '@common/redis/types/redis.types';
import { SecurityService } from '@auth/modules/security/services/security.service';
import * as crypto from 'crypto';

@Injectable()
export class ApiKeyService {
  private readonly logger = new Logger(ApiKeyService.name);

  constructor(
    private readonly redisService: RedisService,
    private readonly securityService: SecurityService,
  ) {}

  /**
   * 验证API Key
   */
  async validateApiKey(apiKey: string, clientIp?: string, userAgent?: string) {
    this.logger.log(`验证API Key: ${apiKey.substring(0, 8)}***`);

    if (!apiKey || typeof apiKey !== 'string') {
      throw new UnauthorizedException('API Key不能为空');
    }

    // 从Redis中获取API Key信息
    const keyData = await this.redisService.get(`auth:apikey:${apiKey}`, 'global' as DataType);
    if (!keyData || typeof keyData !== 'string') {
      throw new UnauthorizedException('无效的API Key');
    }

    const keyInfo = JSON.parse(keyData);

    // 检查API Key是否过期
    if (keyInfo.expiresAt && new Date() > new Date(keyInfo.expiresAt)) {
      throw new UnauthorizedException('API Key已过期');
    }

    // 检查API Key是否被禁用
    if (keyInfo.disabled) {
      throw new UnauthorizedException('API Key已被禁用');
    }

    // 检查IP白名单
    if (keyInfo.ipWhitelist && keyInfo.ipWhitelist.length > 0 && clientIp) {
      const isAllowed = keyInfo.ipWhitelist.some(allowedIp => {
        return clientIp === allowedIp || clientIp.startsWith(allowedIp);
      });
      if (!isAllowed) {
        throw new UnauthorizedException('IP地址不在白名单中');
      }
    }

    // 更新最后使用时间
    await this.updateApiKeyLastUsed(apiKey);

    return {
      id: keyInfo.userId || keyInfo.clientId,
      type: 'api-key',
      name: keyInfo.name,
      permissions: keyInfo.permissions || [],
      rateLimit: keyInfo.rateLimit,
      userId: keyInfo.userId,
      clientId: keyInfo.clientId,
    };
  }

  /**
   * 生成API Key
   */
  async generateApiKey(data: {
    userId: string;
    name: string;
    permissions?: string[];
    expiresAt?: Date;
    rateLimit?: {
      requests: number;
      window: number;
    };
    ipWhitelist?: string[];
  }) {
    this.logger.log(`生成API Key: 用户 ${data.userId}, 名称 ${data.name}`);

    // 生成API Key
    const apiKey = this.generateApiKeyString();

    // 构建API Key信息
    const keyInfo = {
      userId: data.userId,
      name: data.name,
      permissions: data.permissions || [],
      expiresAt: data.expiresAt,
      rateLimit: data.rateLimit,
      ipWhitelist: data.ipWhitelist || [],
      disabled: false,
      createdAt: new Date(),
      lastUsedAt: null,
    };

    // 存储到Redis（如果有过期时间，设置TTL）
    const ttl = data.expiresAt ? Math.floor((data.expiresAt.getTime() - Date.now()) / 1000) : undefined;
    await this.redisService.set(`auth:apikey:${apiKey}`, JSON.stringify(keyInfo), ttl, 'global' as DataType);

    // 记录安全事件
    await this.securityService.logSecurityEvent({
      type: 'api_key_generated' as any,
      userId: data.userId,
      details: {
        apiKeyName: data.name,
        permissions: data.permissions,
        expiresAt: data.expiresAt,
      }
    });

    return {
      apiKey,
      name: data.name,
      permissions: data.permissions,
      expiresAt: data.expiresAt,
      createdAt: keyInfo.createdAt,
    };
  }

  /**
   * 撤销API Key
   */
  async revokeApiKey(apiKey: string): Promise<void> {
    this.logger.log(`撤销API Key: ${apiKey.substring(0, 8)}***`);

    // 从Redis中删除API Key
    await this.redisService.del(`auth:apikey:${apiKey}`, 'global' as DataType);

    // 记录安全事件
    await this.securityService.logSecurityEvent({
      type: 'api_key_revoked' as any,
      details: {
        apiKey: apiKey.substring(0, 8) + '***',
      }
    });
  }

  /**
   * 列出用户的API Keys
   */
  async listApiKeys(userId: string) {
    this.logger.log(`列出用户API Keys: ${userId}`);

    // 这里简化实现，实际应该从数据库查询
    // 或者在Redis中维护用户的API Key列表
    const userApiKeysKey = `auth:user:${userId}:apikeys`;
    const apiKeyIds = await this.redisService.get(userApiKeysKey, 'global' as DataType);
    
    if (!apiKeyIds) {
      return [];
    }

    const keyIds = JSON.parse(apiKeyIds as string);
    const apiKeys = [];

    for (const keyId of keyIds) {
      const keyData = await this.redisService.get(`auth:apikey:${keyId}`, 'global' as DataType);
      if (keyData) {
        const keyInfo = JSON.parse(keyData as string);
        apiKeys.push({
          id: keyId.substring(0, 8) + '***', // 隐藏完整Key
          name: keyInfo.name,
          permissions: keyInfo.permissions,
          createdAt: keyInfo.createdAt,
          lastUsedAt: keyInfo.lastUsedAt,
          expiresAt: keyInfo.expiresAt,
          disabled: keyInfo.disabled,
        });
      }
    }

    return apiKeys;
  }

  /**
   * 更新API Key最后使用时间
   */
  private async updateApiKeyLastUsed(apiKey: string): Promise<void> {
    try {
      const keyData = await this.redisService.get(`auth:apikey:${apiKey}`, 'global' as DataType);
      if (keyData && typeof keyData === 'string') {
        const keyInfo = JSON.parse(keyData);
        keyInfo.lastUsedAt = new Date();
        
        // 保持原有的TTL
        const ttl = keyInfo.expiresAt ? Math.floor((new Date(keyInfo.expiresAt).getTime() - Date.now()) / 1000) : undefined;
        await this.redisService.set(`auth:apikey:${apiKey}`, JSON.stringify(keyInfo), ttl, 'global' as DataType);
      }
    } catch (error) {
      this.logger.error('更新API Key最后使用时间失败', error);
    }
  }

  /**
   * 生成API Key字符串
   */
  private generateApiKeyString(): string {
    const prefix = 'ak_';
    const randomBytes = crypto.randomBytes(32).toString('hex');
    return `${prefix}${randomBytes}`;
  }
}
