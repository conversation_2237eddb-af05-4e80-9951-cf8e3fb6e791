import { Module } from '@nestjs/common';
import { RedisModule } from '@common/redis';
import { SecurityModule } from '@auth/modules/security/security.module';
import { ApiKeyController } from './controllers/api-key.controller';
import { ApiKeyService } from './services/api-key.service';

@Module({
  imports: [
    RedisModule,
    SecurityModule,
  ],
  controllers: [ApiKeyController],
  providers: [ApiKeyService],
  exports: [ApiKeyService],
})
export class ApiKeyModule {}
