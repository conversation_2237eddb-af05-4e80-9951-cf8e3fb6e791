import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ApiKeyService } from '../services/api-key.service';

@Controller()
export class ApiKeyController {
  private readonly logger = new Logger(ApiKeyController.name);

  constructor(
    private readonly apiKeyService: ApiKeyService,
  ) {}

  /**
   * 验证API Key - 微服务调用
   */
  @MessagePattern('validateApiKey')
  async validateApiKeyMicroservice(@Payload() data: { 
    apiKey: string; 
    clientIp?: string;
    userAgent?: string;
  }) {
    console.log(`📨 收到微服务调用: validateApiKey`);
    console.log(`📨 请求数据: ${JSON.stringify({ apiKey: '***', clientIp: data.clientIp })}`);

    try {
      const result = await this.apiKeyService.validateApiKey(data.apiKey, data.clientIp, data.userAgent);
      console.log(`✅ 微服务调用成功: API Key验证完成`);
      return {
        success: true,
        message: 'API Key验证成功',
        data: result,
      };
    } catch (error) {
      console.error(`❌ 微服务调用失败: ${error.message}`);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 生成API Key - 微服务调用
   */
  @MessagePattern('generateApiKey')
  async generateApiKeyMicroservice(@Payload() data: {
    userId: string;
    name: string;
    permissions?: string[];
    expiresAt?: Date;
    rateLimit?: {
      requests: number;
      window: number;
    };
    ipWhitelist?: string[];
  }) {
    console.log(`📨 收到微服务调用: generateApiKey`);
    console.log(`📨 请求数据: ${JSON.stringify(data)}`);

    try {
      const result = await this.apiKeyService.generateApiKey(data);
      console.log(`✅ 微服务调用成功: API Key生成完成`);
      return {
        success: true,
        message: 'API Key生成成功',
        data: result,
      };
    } catch (error) {
      console.error(`❌ 微服务调用失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 撤销API Key - 微服务调用
   */
  @MessagePattern('revokeApiKey')
  async revokeApiKeyMicroservice(@Payload() data: { apiKey: string }) {
    console.log(`📨 收到微服务调用: revokeApiKey`);
    console.log(`📨 请求数据: ${JSON.stringify({ apiKey: '***' })}`);

    try {
      await this.apiKeyService.revokeApiKey(data.apiKey);
      console.log(`✅ 微服务调用成功: API Key撤销完成`);
      return {
        success: true,
        message: 'API Key撤销成功',
      };
    } catch (error) {
      console.error(`❌ 微服务调用失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 列出用户的API Keys - 微服务调用
   */
  @MessagePattern('listApiKeys')
  async listApiKeysMicroservice(@Payload() data: { userId: string }) {
    console.log(`📨 收到微服务调用: listApiKeys`);
    console.log(`📨 请求数据: ${JSON.stringify(data)}`);

    try {
      const result = await this.apiKeyService.listApiKeys(data.userId);
      console.log(`✅ 微服务调用成功: API Key列表获取完成`);
      return {
        success: true,
        message: 'API Key列表获取成功',
        data: result,
      };
    } catch (error) {
      console.error(`❌ 微服务调用失败: ${error.message}`);
      throw error;
    }
  }
}
