import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { Exclude } from 'class-transformer';

// 用户状态枚举
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  DELETED = 'deleted',
}

// 性别枚举
export enum Gender {
  MALE = 'male',
  FEMALE = 'female',
  OTHER = 'other',
}

// 设备类型枚举
export enum DeviceType {
  WEB = 'web',
  MOBILE = 'mobile',
  DESKTOP = 'desktop',
  TABLET = 'tablet',
}

// 用户个人信息子文档
@Schema({ _id: false })
export class UserProfile {
  @ApiProperty({ description: '名字' })
  @Prop({ required: true, trim: true, maxlength: 50 })
  firstName: string;

  @ApiProperty({ description: '姓氏' })
  @Prop({ required: true, trim: true, maxlength: 50 })
  lastName: string;

  @ApiProperty({ description: '头像URL', required: false })
  @Prop({ trim: true })
  avatar?: string;

  @ApiProperty({ description: '出生日期', required: false })
  @Prop()
  dateOfBirth?: Date;

  @ApiProperty({ description: '性别', enum: Gender, required: false })
  @Prop({ enum: Gender })
  gender?: Gender;

  @ApiProperty({ description: '国家代码', required: false })
  @Prop({ trim: true, maxlength: 2 })
  country?: string;

  @ApiProperty({ description: '时区', required: false })
  @Prop({ trim: true })
  timezone?: string;

  @ApiProperty({ description: '首选语言', default: 'zh' })
  @Prop({ trim: true, default: 'zh', maxlength: 5 })
  language: string;
}

// 游戏个人信息子文档
@Schema({ _id: false })
export class GameProfile {
  @ApiProperty({ description: '用户等级', default: 1 })
  @Prop({ default: 1, min: 1, max: 100 })
  level: number;

  @ApiProperty({ description: '经验值', default: 0 })
  @Prop({ default: 0, min: 0 })
  experience: number;

  @ApiProperty({ description: '游戏币', default: 1000 })
  @Prop({ default: 1000, min: 0 })
  coins: number;

  @ApiProperty({ description: '会员到期时间', required: false })
  @Prop()
  premiumUntil?: Date;

  @ApiProperty({ description: '成就列表', type: [String] })
  @Prop({ type: [String], default: [] })
  achievements: string[];

  @ApiProperty({ description: '游戏偏好设置' })
  @Prop({ type: Object, default: {} })
  preferences: Record<string, any>;
}

// 安全设置子文档
@Schema({ _id: false })
export class SecuritySettings {
  @ApiProperty({ description: '是否启用MFA', default: false })
  @Prop({ default: false })
  mfaEnabled: boolean;

  @ApiProperty({ description: 'MFA密钥', required: false })
  @Prop({ select: false })
  mfaSecret?: string;

  @ApiProperty({ description: '可信设备列表', type: [String] })
  @Prop({ type: [String], default: [] })
  trustedDevices: string[];

  @ApiProperty({ description: '最后密码修改时间' })
  @Prop({ default: Date.now })
  lastPasswordChange: Date;

  @ApiProperty({ description: '历史密码哈希', type: [String] })
  @Prop({ type: [String], default: [], select: false })
  passwordHistory: string[];

  @ApiProperty({ description: '登录尝试次数', default: 0 })
  @Prop({ default: 0, min: 0 })
  loginAttempts: number;

  @ApiProperty({ description: '锁定到期时间', required: false })
  @Prop()
  lockedUntil?: Date;

  @ApiProperty({ description: '备用码', type: [String] })
  @Prop({ type: [String], default: [], select: false })
  backupCodes: string[];

  // 管理员功能相关属性
  @ApiProperty({ description: '是否必须更改密码', default: false })
  @Prop({ default: false })
  mustChangePassword: boolean;

  @ApiProperty({ description: '账户是否被管理员锁定', default: false })
  @Prop({ default: false })
  accountLocked: boolean;

  @ApiProperty({ description: '锁定原因', required: false })
  @Prop()
  lockReason?: string;

  @ApiProperty({ description: '锁定时间', required: false })
  @Prop()
  lockedAt?: Date;

  @ApiProperty({ description: '锁定过期时间', required: false })
  @Prop()
  lockExpiresAt?: Date;
}

// 用户主文档
@Schema({
  timestamps: true,
  collection: 'users',
  // 移除toJSON转换，改用专门的转换服务
  // 这样可以避免在Schema层面处理序列化问题
})
export class User {
  @ApiProperty({ description: '用户ID' })
  id?: string;

  @ApiProperty({ description: '用户名', uniqueItems: true })
  @Prop({ 
    required: true, 
    unique: true, 
    trim: true, 
    lowercase: true,
    minlength: 3,
    maxlength: 30,
    match: /^[a-zA-Z0-9_-]+$/
  })
  username: string;

  @ApiProperty({ description: '邮箱地址', uniqueItems: true })
  @Prop({ 
    required: true, 
    unique: true, 
    trim: true, 
    lowercase: true,
    match: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  })
  email: string;

  @ApiProperty({ description: '手机号码', required: false })
  @Prop({ 
    trim: true,
    match: /^\+?[1-9]\d{1,14}$/
  })
  phone?: string;

  @ApiProperty({ description: '密码哈希' })
  @Prop({ required: true, select: false })
  passwordHash: string;

  @ApiProperty({ description: '密码盐值' })
  @Prop({ required: true, select: false })
  salt: string;

  @ApiProperty({ description: '用户个人信息', type: UserProfile })
  @Prop({ type: UserProfile, required: true })
  profile: UserProfile;

  @ApiProperty({ description: '游戏个人信息', type: GameProfile })
  @Prop({ type: GameProfile, default: () => ({}) })
  gameProfile: GameProfile;

  @ApiProperty({ description: '安全设置', type: SecuritySettings })
  @Prop({ type: SecuritySettings, default: () => ({}) })
  security: SecuritySettings;

  @ApiProperty({ description: '用户状态', enum: UserStatus, default: UserStatus.ACTIVE })
  @Prop({ enum: UserStatus, default: UserStatus.ACTIVE })
  status: UserStatus;

  @ApiProperty({ description: '邮箱是否已验证', default: false })
  @Prop({ default: false })
  emailVerified: boolean;

  @ApiProperty({ description: '手机是否已验证', default: false })
  @Prop({ default: false })
  phoneVerified: boolean;

  @ApiProperty({ description: '用户角色', type: [String] })
  @Prop({ type: [String], default: ['user'] })
  roles: string[];

  @ApiProperty({ description: '直接权限', type: [String] })
  @Prop({ type: [String], default: [] })
  permissions: string[];

  @ApiProperty({ description: '最后登录时间', required: false })
  @Prop()
  lastLoginAt?: Date;

  @ApiProperty({ description: '最后活跃时间', required: false })
  @Prop()
  lastActiveAt?: Date;

  @ApiProperty({ description: '最后登录IP', required: false })
  @Prop({ trim: true })
  lastLoginIp?: string;

  @ApiProperty({ description: '注册IP', required: false })
  @Prop({ trim: true })
  registrationIp?: string;

  @ApiProperty({ description: '邮箱验证令牌', required: false })
  @Prop({ select: false })
  emailVerificationToken?: string;

  @ApiProperty({ description: '邮箱验证令牌过期时间', required: false })
  @Prop({ select: false })
  emailVerificationExpires?: Date;

  @ApiProperty({ description: '密码重置令牌', required: false })
  @Prop({ select: false })
  passwordResetToken?: string;

  @ApiProperty({ description: '密码重置令牌过期时间', required: false })
  @Prop({ select: false })
  passwordResetExpires?: Date;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  @ApiProperty({ description: '删除时间（软删除）', required: false })
  @Prop()
  deletedAt?: Date;

  // 虚拟字段：全名
  get fullName(): string {
    return `${this.profile.firstName} ${this.profile.lastName}`;
  }

  // 虚拟字段：是否被锁定
  get isLocked(): boolean {
    return !!(this.security.lockedUntil && this.security.lockedUntil > new Date());
  }

  // 虚拟字段：是否为高级用户
  get isPremium(): boolean {
    return !!(this.gameProfile.premiumUntil && this.gameProfile.premiumUntil > new Date());
  }

  // 虚拟字段：账户年龄（天）
  get accountAge(): number {
    return Math.floor((Date.now() - this.createdAt.getTime()) / (1000 * 60 * 60 * 24));
  }
}

export const UserSchema = SchemaFactory.createForClass(User);

// 创建索引
UserSchema.index({ username: 1 }, { unique: true });
UserSchema.index({ email: 1 }, { unique: true });
UserSchema.index({ phone: 1 }, { sparse: true });
UserSchema.index({ status: 1 });
UserSchema.index({ roles: 1 });
UserSchema.index({ 'gameProfile.level': 1 });
UserSchema.index({ createdAt: 1 });
UserSchema.index({ lastLoginAt: 1 });
UserSchema.index({ lastActiveAt: 1 });

// 复合索引
UserSchema.index({ status: 1, roles: 1 });
UserSchema.index({ emailVerified: 1, phoneVerified: 1 });
UserSchema.index({ 'security.loginAttempts': 1, 'security.lockedUntil': 1 });

// 中间件：保存前处理
UserSchema.pre('save', function(next) {
  // 更新最后活跃时间
  if (this.isModified() && !this.isNew) {
    this.lastActiveAt = new Date();
  }
  
  // 确保用户名和邮箱小写
  if (this.isModified('username')) {
    this.username = this.username.toLowerCase();
  }
  
  if (this.isModified('email')) {
    this.email = this.email.toLowerCase();
  }
  
  next();
});

// 实例方法：检查密码是否需要更新
UserSchema.methods.isPasswordExpired = function(): boolean {
  const maxAge = 90; // 90天
  const daysSinceChange = Math.floor(
    (Date.now() - this.security.lastPasswordChange.getTime()) / (1000 * 60 * 60 * 24)
  );
  return daysSinceChange > maxAge;
};

// 实例方法：增加登录尝试次数
UserSchema.methods.incrementLoginAttempts = async function(): Promise<void> {
  // 如果之前的锁定已过期，重置计数器
  if (this.security.lockedUntil && this.security.lockedUntil < new Date()) {
    await this.updateOne({
      $unset: { 'security.lockedUntil': 1 },
      $set: { 'security.loginAttempts': 1 }
    });
    return;
  }

  const updates: any = { $inc: { 'security.loginAttempts': 1 } };

  // 如果达到最大尝试次数，锁定账户
  const maxAttempts = 5;
  const lockTime = 15 * 60 * 1000; // 15分钟

  if (this.security.loginAttempts + 1 >= maxAttempts && !this.isLocked) {
    updates.$set = { 'security.lockedUntil': new Date(Date.now() + lockTime) };
  }

  await this.updateOne(updates);
};

// 实例方法：重置登录尝试
UserSchema.methods.resetLoginAttempts = async function(): Promise<void> {
  await this.updateOne({
    $unset: {
      'security.loginAttempts': 1,
      'security.lockedUntil': 1
    }
  });
};

/**
 * 用户文档接口 - 数据库文档类型
 *
 * 设计原则：
 * 1. 只包含数据库操作相关的方法
 * 2. 避免循环引用
 * 3. 分离业务逻辑和数据访问
 * 4. JWT属性通过单独的接口扩展
 */
export interface UserDocument extends Omit<User, 'id'>, Document {
  // 检查密码是否过期
  isPasswordExpired(): boolean;

  // 增加登录尝试次数 - 返回void避免循环引用
  incrementLoginAttempts(): Promise<void>;

  // 重置登录尝试次数 - 返回void避免循环引用
  resetLoginAttempts(): Promise<void>;
}

/**
 * JWT扩展用户文档接口
 *
 * 用于在认证过程中临时添加JWT相关属性
 * 这些属性不会被持久化到数据库
 */
export interface JwtUserDocument extends UserDocument {
  // JWT 相关属性（在认证时添加）
  sessionId?: string;
  deviceId?: string;
  jti?: string;
}
