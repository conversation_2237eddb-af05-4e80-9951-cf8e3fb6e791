import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { MongooseModule } from '@nestjs/mongoose';

// 控制器
import { AuthController } from './controllers/auth.controller';
import { MfaController } from './controllers/mfa.controller';
import { CharacterAuthController } from './controllers/character-auth.controller';

// 服务
import { AuthService } from './services/auth.service';
import { JwtService } from './services/jwt.service';
import { MfaService } from './services/mfa.service';
import { CharacterAuthService } from './services/character-auth.service';
import { CharacterSessionService } from './services/character-session.service';

// 策略
import { JwtStrategy } from './strategies/jwt.strategy';
import { LocalStrategy } from './strategies/local.strategy';

// 仓储
import { CharacterSessionRepository, ICharacterSessionRepository } from './repositories/character-session.repository';

// 实体
import { CharacterSession, CharacterSessionSchema } from './entities/character-session.entity';

// 守卫
import { CharacterAuthGuard } from './guards/character-auth.guard';

// 依赖模块 - 使用接口注入避免循环依赖
import { UserModule } from '@auth/modules/user/user.module';
import { SessionModule } from '../session/session.module';
import { SecurityModule } from '../security/security.module';
import { ApiKeyModule } from '../api-key/api-key.module';

/**
 * 认证模块
 *
 * 整合了原auth模块和character-auth模块的功能，包括：
 * - 用户认证（登录、注册、MFA）
 * - JWT Token管理
 * - 角色认证（角色Token、角色会话）
 * - API Key认证（服务间通信）
 * - 认证策略和守卫
 *
 * 职责范围：
 * - 用户身份验证和授权
 * - Token生命周期管理
 * - 多因子认证
 * - 角色认证和会话管理
 * - API Key管理和验证
 * - 认证相关的安全控制
 */
@Module({
  imports: [
    ConfigModule,
    
    // Passport 模块
    PassportModule.register({ 
      defaultStrategy: 'jwt',
      session: false 
    }),
    
    // JWT 模块 - 统一配置
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('auth.jwt.secret'),
        signOptions: {
          expiresIn: configService.get<string>('auth.jwt.accessTokenTTL'),
          issuer: configService.get<string>('auth.jwt.issuer'),
          audience: configService.get<string>('auth.jwt.audience'),
          algorithm: configService.get<string>('auth.jwt.algorithm') as any,
        },
      }),
      inject: [ConfigService],
    }),

    // MongoDB Schema注册 - 只注册本模块需要的Schema
    MongooseModule.forFeature([
      { name: CharacterSession.name, schema: CharacterSessionSchema },
    ]),
    
    // 依赖模块 - 使用模块导入而非直接服务注入
    UserModule,
    SessionModule,
    SecurityModule,
    ApiKeyModule, // ✅ 添加API Key模块
  ],
  controllers: [
    AuthController,
    MfaController,
    CharacterAuthController,
  ],
  providers: [
    // 仓储层
    CharacterSessionRepository,

    // 认证服务
    AuthService,
    JwtService,
    MfaService,

    // 角色认证服务
    CharacterAuthService,
    CharacterSessionService,
    
    // 认证策略
    JwtStrategy,
    LocalStrategy,
    
    // 守卫
    CharacterAuthGuard,

    // 接口实现注册
    {
      provide: 'ICharacterSessionRepository',
      useClass: CharacterSessionRepository,
    },
  ],
  exports: [
    // 仓储层
    CharacterSessionRepository,

    // 核心认证服务
    AuthService,
    JwtService,
    MfaService,

    // 角色认证服务
    CharacterAuthService,
    CharacterSessionService,
    
    // Passport和JWT模块
    PassportModule,
    JwtModule,
    
    // 守卫
    CharacterAuthGuard,

    // 接口导出
    'ICharacterSessionRepository',
  ],
})
export class AuthModule {
  constructor() {
    console.log('✅ Auth模块已初始化 - 包含用户认证和角色认证功能');
  }
}
