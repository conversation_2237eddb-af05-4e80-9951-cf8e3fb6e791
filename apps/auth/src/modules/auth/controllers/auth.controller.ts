import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  UseGuards,
  Request,
  ValidationPipe,
  UseInterceptors,
  ClassSerializerInterceptor,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ThrottlerBehindProxyGuard } from '../../security/guards/throttler-behind-proxy.guard';
import { AuthService, LoginDto, ResetPasswordDto, ConfirmResetDto } from '../services/auth.service';
import { JwtService } from '../services/jwt.service';
import { CreateUserDto } from '@auth/modules/user/dto/create-user.dto';
import { UsersService } from '@auth/modules/user/services/users.service';
import { JwtAuthGuard } from '../../security/guards/jwt-auth.guard';
import { CurrentUser } from '@auth/common/decorators/current-user.decorator';
import { User, UserDocument } from '@auth/modules/user/entities/user.entity';
import { ApiResponseDto } from '@auth/common/dto/response.dto';
import { IsString, IsOptional, IsBoolean, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import {MessagePattern, Payload} from "@nestjs/microservices";
import { VerifyTokenDto, VerifyTokenResponseDto } from '../dto/verify-token.dto';

// 登录DTO
export class LoginRequestDto implements LoginDto {
  @ApiProperty({ 
    description: '用户名或邮箱',
    example: 'john_doe'
  })
  @IsString()
  identifier: string;

  @ApiProperty({ 
    description: '密码',
    example: 'SecurePass123!'
  })
  @IsString()
  password: string;

  @ApiProperty({ 
    description: 'MFA验证码',
    example: '123456',
    required: false
  })
  @IsOptional()
  @IsString()
  mfaCode?: string;

  @ApiProperty({ 
    description: '记住我',
    example: false,
    default: false
  })
  @IsOptional()
  @IsBoolean()
  rememberMe?: boolean;

  @ApiProperty({ 
    description: '设备信息',
    required: false
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => Object)
  deviceInfo?: {
    type: string;
    name: string;
    fingerprint: string;
    userAgent: string;
    ipAddress: string;
  };
}

// 刷新令牌DTO
export class RefreshTokenDto {
  @ApiProperty({ 
    description: '刷新令牌',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
  })
  @IsString()
  refreshToken: string;
}

// 登出DTO
export class LogoutDto {
  @ApiProperty({ 
    description: '刷新令牌',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    required: false
  })
  @IsOptional()
  @IsString()
  refreshToken?: string;

  @ApiProperty({ 
    description: '是否登出所有设备',
    example: false,
    default: false
  })
  @IsOptional()
  @IsBoolean()
  allDevices?: boolean;
}

@ApiTags('认证')
@Controller('auth')
@UseInterceptors(ClassSerializerInterceptor)
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
  ) {}

  @Post('register')
  @UseGuards(ThrottlerBehindProxyGuard)
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: '用户注册' })
  @ApiResponse({
    status: 201,
    description: '注册成功',
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 409,
    description: '用户名或邮箱已存在',
  })
  @ApiResponse({
    status: 429,
    description: '请求过于频繁',
  })
  async register(
    @Body(ValidationPipe) createUserDto: CreateUserDto,
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    const registrationIp = req.ip || req.connection.remoteAddress;
    const user = await this.usersService.create(createUserDto, registrationIp);
    
    return {
      success: true,
      data: {
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          emailVerified: user.emailVerified,
          status: user.status,
        }
      },
      message: '注册成功，请查收邮箱验证邮件',
      timestamp: new Date().toISOString(),
    };
  }

  @Post('login')
  @UseGuards(ThrottlerBehindProxyGuard)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '用户登录' })
  @ApiResponse({
    status: 200,
    description: '登录成功',
  })
  @ApiResponse({
    status: 401,
    description: '用户名或密码错误',
  })
  @ApiResponse({
    status: 429,
    description: '请求过于频繁',
  })
  async login(
    @Body(ValidationPipe) loginDto: LoginRequestDto,
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    // 添加设备信息
    const deviceInfo = {
      type: 'web',
      name: req.headers['user-agent'] || 'Unknown Device',
      fingerprint: loginDto.deviceInfo?.fingerprint || req.headers['x-device-fingerprint'] || 'unknown',
      userAgent: req.headers['user-agent'] || '',
      ipAddress: req.ip || req.connection.remoteAddress || '',
    };

    const result = await this.authService.login({
      ...loginDto,
      deviceInfo,
    });
    
    return {
      success: true,
      data: result,
      message: '登录成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Post('refresh')
  @UseGuards(ThrottlerBehindProxyGuard)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '刷新令牌' })
  @ApiResponse({
    status: 200,
    description: '令牌刷新成功',
  })
  @ApiResponse({
    status: 401,
    description: '刷新令牌无效或已过期',
  })
  @ApiResponse({
    status: 429,
    description: '请求过于频繁',
  })
  async refresh(
    @Body(ValidationPipe) refreshTokenDto: RefreshTokenDto,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.authService.refreshToken(refreshTokenDto.refreshToken);
    
    return {
      success: true,
      data: result,
      message: '令牌刷新成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '用户登出' })
  @ApiResponse({
    status: 204,
    description: '登出成功',
  })
  @ApiResponse({
    status: 401,
    description: '未授权访问',
  })
  async logout(
    @CurrentUser() user: UserDocument,
    @Body(ValidationPipe) logoutDto: LogoutDto,
    @Request() req: any,
  ): Promise<ApiResponseDto<null>> {
    const sessionId = req.user?.sessionId;
    
    await this.authService.logout(user.id, sessionId, logoutDto.allDevices);
    
    return {
      success: true,
      data: null,
      message: logoutDto.allDevices ? '所有设备登出成功' : '登出成功',
      timestamp: new Date().toISOString(),
    };
  }

  @Post('reset-password')
  @UseGuards(ThrottlerBehindProxyGuard)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '发起密码重置' })
  @ApiResponse({
    status: 204,
    description: '密码重置邮件已发送',
  })
  @ApiResponse({
    status: 429,
    description: '请求过于频繁',
  })
  async resetPassword(
    @Body(ValidationPipe) resetPasswordDto: ResetPasswordDto,
  ): Promise<ApiResponseDto<null>> {
    await this.authService.resetPassword(resetPasswordDto);
    
    return {
      success: true,
      data: null,
      message: '如果邮箱存在，密码重置邮件已发送',
      timestamp: new Date().toISOString(),
    };
  }

  @Post('confirm-reset')
  @UseGuards(ThrottlerBehindProxyGuard)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: '确认密码重置' })
  @ApiResponse({
    status: 204,
    description: '密码重置成功',
  })
  @ApiResponse({
    status: 400,
    description: '重置令牌无效或密码不符合要求',
  })
  @ApiResponse({
    status: 429,
    description: '请求过于频繁',
  })
  async confirmReset(
    @Body(ValidationPipe) confirmResetDto: ConfirmResetDto,
  ): Promise<ApiResponseDto<null>> {
    await this.authService.confirmReset(confirmResetDto);
    
    return {
      success: true,
      data: null,
      message: '密码重置成功，请使用新密码登录',
      timestamp: new Date().toISOString(),
    };
  }

  @Post('verify-token')
  @UseGuards(ThrottlerBehindProxyGuard)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '验证令牌有效性',
    description: '验证JWT访问令牌的有效性，包括签名、过期时间、用户状态等检查'
  })
  @ApiResponse({
    status: 200,
    description: '令牌验证结果',
    type: VerifyTokenResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        error: {
          type: 'object',
          properties: {
            code: { type: 'string', example: 'VALIDATION_FAILED' },
            message: { type: 'string', example: '令牌格式不正确' },
            details: { type: 'array', items: { type: 'string' } },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 429,
    description: '请求过于频繁',
  })
  async verifyTokenHttp(
    @Body(ValidationPipe) verifyTokenDto: VerifyTokenDto
  ): Promise<ApiResponseDto<VerifyTokenResponseDto>> {
    const result = await this.authService.verifyToken(verifyTokenDto.token);

    // 根据验证结果设置HTTP状态码
    const httpStatus = result.valid ? HttpStatus.OK : HttpStatus.UNAUTHORIZED;

    return {
      success: result.valid,
      data: result as VerifyTokenResponseDto,
      message: result.valid ? '令牌验证成功' : result.error || '令牌验证失败',
      timestamp: new Date().toISOString(),
    };
  }

  @Post('user-info')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '获取当前用户信息' })
  @ApiResponse({
    status: 200,
    description: '获取用户信息成功',
  })
  @ApiResponse({
    status: 401,
    description: '令牌无效或已过期',
  })
  async getCurrentUserInfo(
    @CurrentUser() user: UserDocument,
  ): Promise<ApiResponseDto<any>> {
    return {
      success: true,
      data: {
        valid: true,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          roles: user.roles,
          permissions: user.permissions,
        }
      },
      message: '获取用户信息成功',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 验证令牌 - 微服务调用
   */
  @MessagePattern('auth.verifyToken')
  async verifyTokenMicroservice(@Payload() data: { token: string }) {
    console.log(`📨 收到微服务调用: verifyToken`);
    console.log(`📨 请求数据: ${JSON.stringify(data)}`);

    try {
      const result = await this.authService.verifyToken(data.token);
      console.log(`✅ 微服务调用成功: Token验证完成`);

      // 返回Gateway期望的格式
      return {
        success: true,
        data: result,
        message: result.valid ? 'Token验证成功' : 'Token验证失败'
      };
    } catch (error) {
      console.error(`❌ 微服务调用失败: ${error.message}`);
      return {
        success: false,
        error: error.message,
        data: {
          valid: false,
          error: error.message
        }
      };
    }
  }

  /**
   * 检查权限 - 微服务调用
   */
  @MessagePattern('auth.checkPermission')
  async checkPermission(@Payload() data: { userId: string; resource: string; action: string }) {
    console.log(`📨 收到微服务调用: checkPermission`);
    console.log(`📨 请求数据: ${JSON.stringify(data)}`);

    try {
      const result = await this.authService.checkPermission(data.userId, data.resource, data.action);
      console.log(`✅ 微服务调用成功: ${result}`);
      return { hasPermission: result };
    } catch (error) {
      console.error(`❌ 微服务调用失败: ${error.message}`);
      return { hasPermission: false, error: error.message };
    }
  }

  /**
   * 获取用户信息 - 微服务调用
   */
  @MessagePattern('auth.getUserInfo')
  async getUserInfo(@Payload() data: { userId: string }) {
    console.log(`📨 收到微服务调用: getUserInfo`);
    console.log(`📨 请求数据: ${JSON.stringify(data)}`);

    try {
      const result = await this.authService.getUserInfo(data.userId);
      console.log(`✅ 微服务调用成功: ${JSON.stringify(result)}`);
      return result;
    } catch (error) {
      console.error(`❌ 微服务调用失败: ${error.message}`);
      return { error: error.message };
    }
  }

  /**
   * 用户登录 - 微服务调用
   */
  @MessagePattern('auth.login')
  async loginMicroservice(@Payload() data: LoginRequestDto) {
    console.log(`📨 收到微服务调用: login`);
    console.log(`📨 请求数据: ${JSON.stringify({ identifier: data.identifier })}`);

    try {
      const result = await this.authService.login(data);
      console.log(`✅ 微服务调用成功: 登录完成`);
      return {
        success: true,
        data: result,
        message: '登录成功'
      };
    } catch (error) {
      console.error(`❌ 微服务调用失败: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 用户登出 - 微服务调用
   */
  @MessagePattern('auth.logout')
  async logoutMicroservice(@Payload() data: { userId: string; token: string }) {
    console.log(`📨 收到微服务调用: logout`);
    console.log(`📨 请求数据: ${JSON.stringify({ userId: data.userId })}`);

    try {
      await this.authService.logout(data.userId, data.token);
      console.log(`✅ 微服务调用成功: 登出完成`);
      return {
        success: true,
        message: '登出成功'
      };
    } catch (error) {
      console.error(`❌ 微服务调用失败: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 刷新Token - 微服务调用
   */
  @MessagePattern('auth.refreshToken')
  async refreshTokenMicroservice(@Payload() data: { refreshToken: string }) {
    console.log(`📨 收到微服务调用: refreshToken`);
    console.log(`📨 请求数据: Token刷新请求`);

    try {
      const result = await this.authService.refreshToken(data.refreshToken);
      console.log(`✅ 微服务调用成功: Token刷新完成`);
      return {
        success: true,
        data: result,
        message: 'Token刷新成功'
      };
    } catch (error) {
      console.error(`❌ 微服务调用失败: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }
}
