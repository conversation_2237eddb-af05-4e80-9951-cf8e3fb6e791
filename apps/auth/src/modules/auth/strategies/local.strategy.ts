import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-local';
import { AuthService } from '../services/auth.service';

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
  constructor(private authService: AuthService) {
    super({
      usernameField: 'identifier', // 支持用户名或邮箱登录
      passwordField: 'password',
    });
  }

  async validate(identifier: string, password: string): Promise<any> {
    try {
      const result = await this.authService.login({
        identifier,
        password,
      });
      
      return result.user;
    } catch (error) {
      throw new UnauthorizedException('用户名或密码错误');
    }
  }
}
