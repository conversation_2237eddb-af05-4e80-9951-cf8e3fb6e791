import {
  Injectable,
  Logger,
  UnauthorizedException,
  BadRequestException,
  NotFoundException
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { MICROSERVICE_NAMES } from '@shared/constants';
import { UsersService } from '@auth/modules/user/services/users.service';
import { JwtService, JwtPayload, TokenPair } from './jwt.service';
import { SessionService } from '../../session/services/session.service';
import { SecurityService, SecurityEventType } from '../../security/services/security.service';
import { MfaService } from './mfa.service';
import { User, UserDocument } from '@auth/modules/user/entities/user.entity';
import * as crypto from 'crypto';
import { PasswordService } from '../../security/services/password.service';

// 角色信息接口（从已删除的server-integration模块迁移）
export interface CharacterInfo {
  characterId: string;
  userId: string;
  serverId: string;
  name: string;
  level: number;
  createdAt: Date;
  lastActiveAt: Date;
}

export interface LoginDto {
  identifier: string;    // 用户名或邮箱
  password: string;      // 密码
  mfaCode?: string;      // MFA验证码
  rememberMe?: boolean;  // 记住我
  deviceInfo?: DeviceInfo;
}

export interface DeviceInfo {
  type: string;          // 设备类型
  name: string;          // 设备名称
  fingerprint: string;   // 设备指纹
  userAgent: string;     // 用户代理
  ipAddress: string;     // IP地址
}

export interface AuthResult {
  user: Partial<UserDocument>;
  tokens: TokenPair;
  session: {
    id: string;
    deviceId: string;
    lastActivity: Date;
  };
}

export interface RefreshResult {
  tokens: TokenPair;
}

export interface ResetPasswordDto {
  email: string;
}

export interface ConfirmResetDto {
  token: string;
  newPassword: string;
}

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly usersService: UsersService,
    private readonly passwordService: PasswordService,
    private readonly jwtService: JwtService,
    private readonly sessionService: SessionService,
    private readonly securityService: SecurityService,
    private readonly mfaService: MfaService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 用户登录
   */
  async login(loginDto: LoginDto): Promise<AuthResult> {
    this.logger.log(`用户登录尝试: ${loginDto.identifier}`);

    // 查找用户
    const user = await this.usersService.findByUsernameOrEmail(loginDto.identifier, true);
    
    // 验证用户状态
    this.usersService.validateUserStatus(user);

    // 验证密码
    const isPasswordValid = await this.passwordService.verifyPassword(
      loginDto.password,
      user.passwordHash
    );

    if (!isPasswordValid) {
      // 增加登录尝试次数
      await this.usersService.incrementLoginAttempts(user);

      // 记录登录失败事件
      await this.securityService.logSecurityEvent({
        type: SecurityEventType.LOGIN_FAILURE,
        userId: user.id,
        ipAddress: loginDto.deviceInfo?.ipAddress,
        userAgent: loginDto.deviceInfo?.userAgent,
        details: {
          identifier: loginDto.identifier,
          reason: 'invalid_password'
        }
      });

      throw new UnauthorizedException('用户名或密码错误');
    }

    // 检查是否需要MFA验证
    if (user.security.mfaEnabled) {
      if (!loginDto.mfaCode) {
        throw new UnauthorizedException('需要提供MFA验证码');
      }

      // 验证MFA代码
      const isMfaValid = await this.mfaService.verifyTotpCode(user.id, loginDto.mfaCode) ||
                         await this.mfaService.verifyBackupCode(user.id, loginDto.mfaCode);

      if (!isMfaValid) {
        throw new UnauthorizedException('MFA验证码错误');
      }
    }

    // 重置登录尝试次数
    await this.usersService.resetLoginAttempts(user);

    // 创建会话
    const session = await this.sessionService.createSession({
      userId: user.id,
      deviceInfo: loginDto.deviceInfo,
      rememberMe: loginDto.rememberMe,
    });

    // 生成JWT令牌
    const tokens = this.jwtService.generateTokenPair({
      sub: user.id,
      username: user.username,
      email: user.email,
      roles: user.roles,
      permissions: user.permissions,
      sessionId: session.id,
      deviceId: session.deviceId,
    });

    // 更新最后登录信息
    await this.usersService.updateLastLogin(user.id, loginDto.deviceInfo?.ipAddress);

    // 更新会话令牌信息
    await this.sessionService.updateSessionTokens(session.id, {
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken,
    });

    // 记录登录成功事件
    await this.securityService.logSecurityEvent({
      type: SecurityEventType.LOGIN_SUCCESS,
      userId: user.id,
      sessionId: session.id,
      ipAddress: loginDto.deviceInfo?.ipAddress,
      userAgent: loginDto.deviceInfo?.userAgent,
      details: {
        deviceId: session.deviceId,
        rememberMe: loginDto.rememberMe
      }
    });

    this.logger.log(`用户登录成功: ${user.username} (${user.id})`);

    // 返回结果（不包含敏感信息）
    const { passwordHash, salt, security, ...safeUser } = user.toObject();
    
    return {
      user: {
        ...safeUser,
        security: {
          mfaEnabled: security.mfaEnabled,
          trustedDevices: security.trustedDevices,
          lastPasswordChange: security.lastPasswordChange,
        }
      },
      tokens,
      session: {
        id: session.id,
        deviceId: session.deviceId,
        lastActivity: session.lastActivity,
      },
    };
  }

  /**
   * 刷新令牌
   */
  async refreshToken(refreshToken: string): Promise<RefreshResult> {
    this.logger.log('刷新令牌请求');

    // 验证刷新令牌
    const payload = await this.jwtService.verifyRefreshToken(refreshToken);

    // 验证会话
    const session = await this.sessionService.findById(payload.sessionId);
    if (!session || !session.active) {
      throw new UnauthorizedException('会话无效或已过期');
    }

    // 验证用户
    const user = await this.usersService.findById(payload.sub);
    this.usersService.validateUserStatus(user);

    // 如果启用了令牌轮换，撤销旧的刷新令牌
    if (this.configService.get<boolean>('auth.jwt.enableTokenRotation')) {
      await this.jwtService.revokeToken(refreshToken);
    }

    // 生成新的令牌对
    const tokens = this.jwtService.generateTokenPair({
      sub: user.id,
      username: user.username,
      email: user.email,
      roles: user.roles,
      permissions: user.permissions,
      sessionId: session.id,
      deviceId: session.deviceId,
    });

    // 更新会话令牌信息
    await this.sessionService.updateSessionTokens(session.id, {
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken,
    });

    // 更新会话活动时间
    await this.sessionService.updateLastActivity(session.id);

    this.logger.log(`令牌刷新成功: ${user.username} (${user.id})`);

    return { tokens };
  }

  /**
   * 用户登出
   */
  async logout(userId: string, sessionId?: string, allDevices = false): Promise<void> {
    this.logger.log(`用户登出: ${userId}, 会话: ${sessionId}, 所有设备: ${allDevices}`);

    if (allDevices) {
      // 登出所有设备
      const sessions = await this.sessionService.findByUserId(userId);
      
      for (const session of sessions) {
        await this.sessionService.terminateSession(session.id);
      }
      
      // 撤销用户所有JWT令牌
      await this.jwtService.revokeAllUserTokens(userId);
      
      this.logger.log(`用户所有设备登出成功: ${userId}`);
    } else if (sessionId) {
      // 登出特定会话
      await this.sessionService.terminateSession(sessionId);
      this.logger.log(`用户单设备登出成功: ${userId}, 会话: ${sessionId}`);
    } else {
      // 如果没有提供会话ID，登出所有会话
      await this.logout(userId, undefined, true);
    }
  }

  /**
   * 验证令牌（增强版）
   */
  async verifyToken(token: string): Promise<{
    valid: boolean;
    user?: any;
    token?: any;
    error?: string;
    errorCode?: string;
  }> {
    const startTime = Date.now();

    try {
      // 1. 基础格式验证
      if (!token || typeof token !== 'string') {
        throw new UnauthorizedException('令牌格式无效');
      }

      // 2. JWT格式验证
      const tokenParts = token.split('.');
      if (tokenParts.length !== 3) {
        throw new UnauthorizedException('令牌格式不正确');
      }

      // 3. 验证JWT令牌（包含签名、过期时间、发行者等验证）
      const payload = await this.jwtService.verifyAccessToken(token);

      // 4. 验证令牌类型
      if (payload.type !== 'access') {
        throw new UnauthorizedException('令牌类型错误');
      }

      // 5. 获取用户信息
      const user = await this.usersService.findById(payload.sub);

      // 6. 验证用户状态
      this.usersService.validateUserStatus(user);

      // 7. 验证会话状态（如果有sessionId）
      if (payload.sessionId) {
        await this.validateSessionStatus(payload.sessionId);
      }

      // 8. 记录成功的验证日志
      this.logger.log(`令牌验证成功: 用户=${payload.sub}, 耗时=${Date.now() - startTime}ms`);

      // 9. 返回安全的用户信息和令牌信息
      return {
        valid: true,
        user: this.buildSafeUserInfo(user),
        token: this.buildTokenInfo(payload),
      };
    } catch (error) {
      // 记录详细的错误日志
      this.logger.warn(`令牌验证失败: ${error.message}, 耗时=${Date.now() - startTime}ms`);

      // 返回结构化的错误信息
      return this.buildErrorResponse(error);
    }
  }

  /**
   * 验证令牌（内部方法）
   */
  async validateToken(userId: string): Promise<UserDocument> {
    // 获取用户信息
    const user = await this.usersService.findById(userId);
    this.usersService.validateUserStatus(user);

    return user;
  }

  /**
   * 验证会话状态
   */
  private async validateSessionStatus(sessionId: string): Promise<void> {
    try {
      const session = await this.sessionService.findById(sessionId);
      if (!session || !session.active) {
        throw new UnauthorizedException('会话已失效');
      }

      if (session.expiresAt < new Date()) {
        throw new UnauthorizedException('会话已过期');
      }
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      // 如果会话服务不可用，记录警告但不阻止验证
      this.logger.warn(`会话验证失败: ${error.message}`);
    }
  }

  /**
   * 构建安全的用户信息
   */
  private buildSafeUserInfo(user: UserDocument): any {
    return {
      id: user._id?.toString() || user.id,
      username: user.username,
      email: user.email,
      roles: user.roles || [],
      permissions: user.permissions || [],
      status: user.status,
      emailVerified: user.emailVerified || false,
      profile: user.profile ? {
        firstName: user.profile.firstName,
        lastName: user.profile.lastName,
        language: user.profile.language,
      } : null,
      security: {
        mfaEnabled: user.security?.mfaEnabled || false,
        lastPasswordChange: user.security?.lastPasswordChange,
      }
    };
  }

  /**
   * 构建令牌信息
   */
  private buildTokenInfo(payload: any): any {
    const now = Math.floor(Date.now() / 1000);
    return {
      jti: payload.jti,
      iat: payload.iat,
      exp: payload.exp,
      expiresIn: Math.max(0, payload.exp - now),
      type: payload.type || 'access',
    };
  }

  /**
   * 构建错误响应
   */
  private buildErrorResponse(error: any): {
    valid: boolean;
    error: string;
    errorCode: string;
  } {
    let errorCode = 'TOKEN_INVALID';
    let errorMessage = '令牌无效';

    if (error.name === 'TokenExpiredError') {
      errorCode = 'TOKEN_EXPIRED';
      errorMessage = '令牌已过期';
    } else if (error.name === 'JsonWebTokenError') {
      errorCode = 'TOKEN_MALFORMED';
      errorMessage = '令牌格式错误';
    } else if (error.name === 'NotBeforeError') {
      errorCode = 'TOKEN_NOT_ACTIVE';
      errorMessage = '令牌尚未生效';
    } else if (error.message?.includes('撤销')) {
      errorCode = 'TOKEN_REVOKED';
      errorMessage = '令牌已被撤销';
    } else if (error.message?.includes('会话')) {
      errorCode = 'SESSION_INVALID';
      errorMessage = error.message;
    } else if (error.message?.includes('用户')) {
      errorCode = 'USER_INVALID';
      errorMessage = error.message;
    }

    return {
      valid: false,
      error: errorMessage,
      errorCode,
    };
  }

  /**
   * 发起密码重置
   */
  async resetPassword(resetPasswordDto: ResetPasswordDto): Promise<void> {
    this.logger.log(`密码重置请求: ${resetPasswordDto.email}`);

    try {
      const user = await this.usersService.findByEmail(resetPasswordDto.email);
      
      // 生成重置令牌
      const resetToken = this.passwordService.generateResetToken();
      const resetExpires = new Date(Date.now() + 3600000); // 1小时后过期

      // 保存重置令牌
      await this.usersService.update(user.id, {
        passwordResetToken: resetToken,
        passwordResetExpires: resetExpires,
      } as any);

      // TODO: 发送密码重置邮件
      // await this.emailService.sendPasswordResetEmail(user.email, resetToken);

      this.logger.log(`密码重置邮件已发送: ${resetPasswordDto.email}`);
    } catch (error) {
      // 为了安全，即使用户不存在也不抛出错误
      this.logger.warn(`密码重置请求的邮箱不存在: ${resetPasswordDto.email}`);
    }
  }

  /**
   * 确认密码重置
   */
  async confirmReset(confirmResetDto: ConfirmResetDto): Promise<void> {
    this.logger.log('确认密码重置');

    // TODO: 实现根据重置令牌查找用户的方法
    // const user = await this.usersService.findByResetToken(confirmResetDto.token);
    // if (!user) {
    //   throw new BadRequestException('无效或已过期的重置令牌');
    // }

    throw new BadRequestException('密码重置功能暂未实现');
  }

  /**
   * 检查权限
   */
  async checkPermission(userId: string, resource: string, action: string): Promise<boolean> {
    const user = await this.usersService.findById(userId);

    // 检查直接权限
    const directPermission = `${resource}:${action}`;
    if (user.permissions.includes(directPermission) || user.permissions.includes('*')) {
      return true;
    }

    // TODO: 检查角色权限
    // const hasRolePermission = await this.roleService.checkRolePermission(user.roles, resource, action);
    // return hasRolePermission;

    return false;
  }

  /**
   * 获取用户信息
   */
  async getUserInfo(userId: string): Promise<any> {
    const user = await this.usersService.findById(userId);
    this.usersService.validateUserStatus(user);

    // 返回安全的用户信息
    const { passwordHash, salt, security, ...safeUser } = user.toObject();

    return {
      ...safeUser,
      security: {
        mfaEnabled: security.mfaEnabled,
        lastPasswordChange: security.lastPasswordChange,
      }
    };
  }

  /**
   * 创建会话 - 微服务接口
   */
  async createSession(createSessionDto: {
    userId: string;
    deviceInfo?: {
      type: string;
      name: string;
      fingerprint: string;
      userAgent: string;
      ipAddress: string;
    };
    rememberMe?: boolean;
  }) {
    this.logger.log(`创建会话: 用户 ${createSessionDto.userId}`);

    const session = await this.sessionService.createSession({
      userId: createSessionDto.userId,
      deviceInfo: createSessionDto.deviceInfo ? {
        type: createSessionDto.deviceInfo.type as any,
        name: createSessionDto.deviceInfo.name,
        fingerprint: createSessionDto.deviceInfo.fingerprint,
        userAgent: createSessionDto.deviceInfo.userAgent,
        ipAddress: createSessionDto.deviceInfo.ipAddress,
      } : undefined,
      rememberMe: createSessionDto.rememberMe,
    });

    return {
      sessionId: session.sessionId,
      userId: session.userId,
      deviceId: session.deviceId,
      expiresAt: session.expiresAt,
      trusted: session.trusted,
    };
  }

  /**
   * 验证会话 - 微服务接口
   */
  async validateSession(sessionId: string) {
    this.logger.log(`验证会话: ${sessionId}`);

    const session = await this.sessionService.findById(sessionId);
    if (!session) {
      throw new UnauthorizedException('会话不存在');
    }

    if (!session.active || session.expiresAt < new Date()) {
      throw new UnauthorizedException('会话已过期');
    }

    // 更新最后活动时间
    await this.sessionService.updateLastActivity(sessionId);

    // 获取用户信息
    const user = await this.usersService.findById(session.userId);
    if (!user) {
      throw new UnauthorizedException('用户不存在');
    }

    return {
      sessionId: session.sessionId,
      userId: session.userId,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        roles: user.roles,
        permissions: user.permissions,
      },
      deviceId: session.deviceId,
      trusted: session.trusted,
      lastActivity: session.lastActivity,
    };
  }
}
