import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AuditService } from './audit.service';
import { RiskService } from './risk.service';
import { EncryptionService } from './encryption.service';

export interface SecurityEvent {
  type: SecurityEventType;
  userId?: string;
  targetUserId?: string;  // 目标用户ID（用于管理员操作）
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
  resource?: string;
  action?: string;
  details?: Record<string, any>;
  riskScore?: number;
  timestamp: Date;
}

export enum SecurityEventType {
  LOGIN_SUCCESS = 'login_success',
  LOGIN_FAILURE = 'login_failure',
  LOGOUT = 'logout',
  PASSWORD_CHANGE = 'password_change',
  MFA_ENABLED = 'mfa_enabled',
  MFA_DISABLED = 'mfa_disabled',
  ACCOUNT_LOCKED = 'account_locked',
  ACCOUNT_UNLOCKED = 'account_unlocked',
  SUSPICIOUS_ACTIVITY = 'suspicious_activity',
  PERMISSION_DENIED = 'permission_denied',
  DATA_ACCESS = 'data_access',
  ADMIN_ACTION = 'admin_action',
  TOKEN_REVOKED = 'token_revoked',
  SESSION_EXPIRED = 'session_expired',
  BRUTE_FORCE_ATTEMPT = 'brute_force_attempt',
  ANOMALY_DETECTED = 'anomaly_detected',
  // 管理员相关事件
  ADMIN_DASHBOARD_ACCESS = 'admin_dashboard_access',
  SYSTEM_MAINTENANCE = 'system_maintenance',
  SYSTEM_BACKUP = 'system_backup',
  SYSTEM_SETTINGS_UPDATE = 'system_settings_update',
  USER_STATUS_CHANGE = 'user_status_change',
  USER_ROLES_CHANGE = 'user_roles_change',
  PASSWORD_RESET_BY_ADMIN = 'password_reset_by_admin',
  ACCOUNT_LOCKED_BY_ADMIN = 'account_locked_by_admin',
  ACCOUNT_UNLOCKED_BY_ADMIN = 'account_unlocked_by_admin',
  MFA_DISABLED_BY_ADMIN = 'mfa_disabled_by_admin',
  SESSION_TERMINATED_BY_ADMIN = 'session_terminated_by_admin',
  USER_DELETED_BY_ADMIN = 'user_deleted_by_admin',
  USER_RESTORED_BY_ADMIN = 'user_restored_by_admin',
}

export interface SecurityAlert {
  id: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  events: SecurityEvent[];
  userId?: string;
  ipAddress?: string;
  createdAt: Date;
  resolved: boolean;
  resolvedAt?: Date;
  resolvedBy?: string;
}

@Injectable()
export class SecurityService {
  private readonly logger = new Logger(SecurityService.name);

  constructor(
    private configService: ConfigService,
    private auditService: AuditService,
    private riskService: RiskService,
    private encryptionService: EncryptionService,
  ) {}

  /**
   * 记录安全事件
   */
  async logSecurityEvent(event: Omit<SecurityEvent, 'timestamp'>): Promise<void> {
    const securityEvent: SecurityEvent = {
      ...event,
      timestamp: new Date(),
    };

    // 计算风险评分
    if (!securityEvent.riskScore) {
      securityEvent.riskScore = await this.riskService.calculateRiskScore({
        eventType: event.type,
        userId: event.userId,
        ipAddress: event.ipAddress,
        userAgent: event.userAgent,
        timestamp: securityEvent.timestamp,
      });
    }

    // 记录审计日志
    await this.auditService.log({
      type: 'security',
      event: event.type,
      userId: event.userId,
      sessionId: event.sessionId,
      ipAddress: event.ipAddress,
      userAgent: event.userAgent,
      resource: event.resource,
      action: event.action,
      details: {
        ...event.details,
        riskScore: securityEvent.riskScore,
      },
      timestamp: securityEvent.timestamp,
    });

    // 检查是否需要生成安全告警
    await this.checkForAlerts(securityEvent);

    this.logger.log(`安全事件记录: ${event.type}`, {
      userId: event.userId,
      riskScore: securityEvent.riskScore,
    });
  }

  /**
   * 检测异常行为
   */
  async detectAnomalies(userId: string, activity: any): Promise<boolean> {
    const anomalies = [];

    // 地理位置异常检测
    if (this.configService.get<boolean>('security.accountProtection.anomalyDetection.geoLocation.enabled')) {
      const geoAnomaly = await this.detectGeoLocationAnomaly(userId, activity);
      if (geoAnomaly) {
        anomalies.push('geo_location');
      }
    }

    // 设备指纹异常检测
    if (this.configService.get<boolean>('security.accountProtection.anomalyDetection.deviceFingerprint.enabled')) {
      const deviceAnomaly = await this.detectDeviceAnomaly(userId, activity);
      if (deviceAnomaly) {
        anomalies.push('device_fingerprint');
      }
    }

    // 行为模式异常检测
    if (this.configService.get<boolean>('security.accountProtection.anomalyDetection.behaviorAnalysis.enabled')) {
      const behaviorAnomaly = await this.detectBehaviorAnomaly(userId, activity);
      if (behaviorAnomaly) {
        anomalies.push('behavior_pattern');
      }
    }

    // 如果检测到异常，记录安全事件
    if (anomalies.length > 0) {
      await this.logSecurityEvent({
        type: SecurityEventType.ANOMALY_DETECTED,
        userId,
        ipAddress: activity.ipAddress,
        userAgent: activity.userAgent,
        details: {
          anomalies,
          activity,
        },
      });

      return true;
    }

    return false;
  }

  /**
   * 检查暴力破解攻击
   */
  async checkBruteForceAttack(identifier: string, ipAddress: string): Promise<boolean> {
    const timeWindow = 300; // 5分钟
    const maxAttempts = 10;

    // 检查IP地址的失败尝试次数
    const ipFailures = await this.auditService.countEvents({
      type: 'security',
      event: SecurityEventType.LOGIN_FAILURE,
      ipAddress,
      timeRange: {
        start: new Date(Date.now() - timeWindow * 1000),
        end: new Date(),
      },
    });

    // 检查用户标识符的失败尝试次数
    const userFailures = await this.auditService.countEvents({
      type: 'security',
      event: SecurityEventType.LOGIN_FAILURE,
      details: { identifier },
      timeRange: {
        start: new Date(Date.now() - timeWindow * 1000),
        end: new Date(),
      },
    });

    const isBruteForce = ipFailures >= maxAttempts || userFailures >= maxAttempts;

    if (isBruteForce) {
      await this.logSecurityEvent({
        type: SecurityEventType.BRUTE_FORCE_ATTEMPT,
        ipAddress,
        details: {
          identifier,
          ipFailures,
          userFailures,
          timeWindow,
        },
      });
    }

    return isBruteForce;
  }

  /**
   * 验证输入安全性
   */
  validateInput(input: string, type: 'sql' | 'xss' | 'general' = 'general'): boolean {
    if (!this.configService.get<boolean>('security.inputValidation.enabled')) {
      return true;
    }

    switch (type) {
      case 'sql':
        return this.validateSqlInjection(input);
      case 'xss':
        return this.validateXss(input);
      default:
        return this.validateSqlInjection(input) && this.validateXss(input);
    }
  }

  /**
   * 数据脱敏
   */
  maskSensitiveData(data: any, type: 'email' | 'phone' | 'idCard'): string {
    if (!this.configService.get<boolean>('security.dataProtection.masking.enabled')) {
      return data;
    }

    const config = this.configService.get(`security.dataProtection.masking.${type}`);
    if (!config) {
      return data;
    }

    const { pattern, visibleChars } = config;

    switch (pattern) {
      case 'prefix':
        return this.maskPrefix(data, visibleChars);
      case 'suffix':
        return this.maskSuffix(data, visibleChars);
      case 'middle':
        return this.maskMiddle(data, visibleChars);
      case 'both':
        return this.maskBoth(data, visibleChars);
      default:
        return data;
    }
  }

  /**
   * 生成安全报告
   */
  async generateSecurityReport(timeRange: { start: Date; end: Date }): Promise<any> {
    const [
      totalEvents,
      eventsByType,
      topRiskyUsers,
      topRiskyIPs,
      alerts,
    ] = await Promise.all([
      this.auditService.countEvents({
        type: 'security',
        timeRange,
      }),
      this.auditService.getEventsByType('security', timeRange),
      this.auditService.getTopRiskyUsers(timeRange),
      this.auditService.getTopRiskyIPs(timeRange),
      this.getActiveAlerts(),
    ]);

    return {
      summary: {
        totalEvents,
        timeRange,
        generatedAt: new Date(),
      },
      eventsByType,
      topRiskyUsers,
      topRiskyIPs,
      alerts: {
        total: alerts.length,
        critical: alerts.filter(a => a.severity === 'critical').length,
        high: alerts.filter(a => a.severity === 'high').length,
        medium: alerts.filter(a => a.severity === 'medium').length,
        low: alerts.filter(a => a.severity === 'low').length,
      },
    };
  }

  /**
   * 检查安全告警
   */
  private async checkForAlerts(event: SecurityEvent): Promise<void> {
    const alertThresholds = this.configService.get('security.audit.alerting.thresholds');
    
    if (!alertThresholds) {
      return;
    }

    // 检查失败登录告警
    if (event.type === SecurityEventType.LOGIN_FAILURE) {
      await this.checkFailedLoginAlert(event, alertThresholds.failedLogins);
    }

    // 检查可疑活动告警
    if (event.riskScore && event.riskScore >= 80) {
      await this.checkSuspiciousActivityAlert(event, alertThresholds.suspiciousActivity);
    }

    // 检查数据泄露告警
    if (event.type === SecurityEventType.DATA_ACCESS && event.details?.sensitive) {
      await this.checkDataBreachAlert(event, alertThresholds.dataBreaches);
    }
  }

  /**
   * 地理位置异常检测
   */
  private async detectGeoLocationAnomaly(userId: string, activity: any): Promise<boolean> {
    // 获取用户最近的登录位置
    const recentLocations = await this.auditService.getRecentUserLocations(userId, 30);
    
    if (recentLocations.length === 0) {
      return false; // 新用户，无历史数据
    }

    const currentLocation = activity.location;
    if (!currentLocation) {
      return false; // 无法获取当前位置
    }

    // 计算与最近位置的距离
    const maxDistance = this.configService.get<number>('security.accountProtection.anomalyDetection.geoLocation.maxDistance');
    const timeThreshold = this.configService.get<number>('security.accountProtection.anomalyDetection.geoLocation.timeThreshold');

    for (const location of recentLocations) {
      const distance = this.calculateDistance(currentLocation, location);
      const timeDiff = Math.abs(activity.timestamp.getTime() - location.timestamp.getTime()) / 1000;

      if (distance > maxDistance && timeDiff < timeThreshold) {
        return true; // 异常：短时间内跨越大距离
      }
    }

    return false;
  }

  /**
   * 设备异常检测
   */
  private async detectDeviceAnomaly(userId: string, activity: any): Promise<boolean> {
    const knownDevices = await this.auditService.getUserKnownDevices(userId);
    const currentDevice = activity.deviceFingerprint;

    if (!currentDevice) {
      return false;
    }

    const isKnownDevice = knownDevices.some(device => device.fingerprint === currentDevice);
    
    if (!isKnownDevice) {
      const strictMode = this.configService.get<boolean>('security.accountProtection.anomalyDetection.deviceFingerprint.strictMode');
      return strictMode; // 严格模式下，新设备视为异常
    }

    return false;
  }

  /**
   * 行为模式异常检测
   */
  private async detectBehaviorAnomaly(userId: string, activity: any): Promise<boolean> {
    // 获取用户行为基线
    const behaviorBaseline = await this.auditService.getUserBehaviorBaseline(userId);
    
    if (!behaviorBaseline) {
      return false; // 无足够数据建立基线
    }

    // 检查登录时间异常
    const currentHour = activity.timestamp.getHours();
    const normalHours = behaviorBaseline.normalLoginHours || [];
    
    if (normalHours.length > 0 && !normalHours.includes(currentHour)) {
      return true; // 异常登录时间
    }

    // 检查访问模式异常
    // TODO: 实现更复杂的行为分析

    return false;
  }

  /**
   * SQL注入验证
   */
  private validateSqlInjection(input: string): boolean {
    const patterns = this.configService.get<string[]>('security.inputValidation.sqlInjection.patterns');
    
    for (const pattern of patterns) {
      if (input.toLowerCase().includes(pattern.toLowerCase())) {
        return false;
      }
    }

    return true;
  }

  /**
   * XSS验证
   */
  private validateXss(input: string): boolean {
    const patterns = this.configService.get<string[]>('security.inputValidation.xss.patterns');
    
    for (const pattern of patterns) {
      if (input.toLowerCase().includes(pattern.toLowerCase())) {
        return false;
      }
    }

    return true;
  }

  /**
   * 数据脱敏 - 前缀模式
   */
  private maskPrefix(data: string, visibleChars: number): string {
    if (data.length <= visibleChars) {
      return '*'.repeat(data.length);
    }
    
    return data.substring(0, visibleChars) + '*'.repeat(data.length - visibleChars);
  }

  /**
   * 数据脱敏 - 后缀模式
   */
  private maskSuffix(data: string, visibleChars: number): string {
    if (data.length <= visibleChars) {
      return '*'.repeat(data.length);
    }
    
    return '*'.repeat(data.length - visibleChars) + data.substring(data.length - visibleChars);
  }

  /**
   * 数据脱敏 - 中间模式
   */
  private maskMiddle(data: string, visibleChars: number): string {
    if (data.length <= visibleChars * 2) {
      return '*'.repeat(data.length);
    }
    
    const start = data.substring(0, visibleChars);
    const end = data.substring(data.length - visibleChars);
    const middle = '*'.repeat(data.length - visibleChars * 2);
    
    return start + middle + end;
  }

  /**
   * 数据脱敏 - 两端模式
   */
  private maskBoth(data: string, visibleChars: number): string {
    if (data.length <= visibleChars * 2) {
      return '*'.repeat(data.length);
    }
    
    const start = data.substring(0, visibleChars);
    const end = data.substring(data.length - visibleChars);
    const middle = '*'.repeat(data.length - visibleChars * 2);
    
    return start + middle + end;
  }

  /**
   * 计算地理距离
   */
  private calculateDistance(loc1: any, loc2: any): number {
    // 使用Haversine公式计算两点间距离
    const R = 6371; // 地球半径（公里）
    const dLat = this.toRadians(loc2.latitude - loc1.latitude);
    const dLon = this.toRadians(loc2.longitude - loc1.longitude);
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(loc1.latitude)) * Math.cos(this.toRadians(loc2.latitude)) *
              Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    
    return R * c;
  }

  /**
   * 角度转弧度
   */
  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * 获取活跃告警
   */
  private async getActiveAlerts(): Promise<SecurityAlert[]> {
    // TODO: 实现告警存储和查询
    return [];
  }

  /**
   * 检查失败登录告警
   */
  private async checkFailedLoginAlert(event: SecurityEvent, threshold: number): Promise<void> {
    // TODO: 实现失败登录告警逻辑
  }

  /**
   * 检查可疑活动告警
   */
  private async checkSuspiciousActivityAlert(event: SecurityEvent, threshold: number): Promise<void> {
    // TODO: 实现可疑活动告警逻辑
  }

  /**
   * 检查数据泄露告警
   */
  private async checkDataBreachAlert(event: SecurityEvent, threshold: number): Promise<void> {
    // TODO: 实现数据泄露告警逻辑
  }
}
