/**
 * Auth服务MessagePattern功能验证脚本
 * 
 * 验证Auth服务的所有MessagePattern是否正常工作
 * 确保Gateway能够正确调用这些微服务接口
 */

const { ClientProxy, ClientProxyFactory, Transport } = require('@nestjs/microservices');

// 配置
const AUTH_SERVICE_CONFIG = {
  transport: Transport.TCP,
  options: {
    host: 'localhost',
    port: 3002, // Auth服务的微服务端口
  }
};

const TEST_USER = {
  username: 'test_messagepattern_user',
  email: '<EMAIL>',
  password: 'TestMP123!',
};

class AuthMessagePatternTester {
  constructor() {
    this.client = null;
    this.testResults = [];
    this.testUserId = null;
    this.testToken = null;
  }

  /**
   * 运行所有MessagePattern测试
   */
  async runAllTests() {
    console.log('🚀 开始Auth服务MessagePattern功能验证');
    console.log('=' .repeat(60));

    try {
      // 连接到Auth服务
      await this.connectToAuthService();
      
      // 测试认证相关MessagePattern
      await this.testAuthMessagePatterns();
      
      // 测试用户管理MessagePattern
      await this.testUserMessagePatterns();
      
      // 测试API Key管理MessagePattern
      await this.testApiKeyMessagePatterns();
      
      // 输出测试结果
      this.printTestResults();
      
    } catch (error) {
      console.error('❌ 测试执行失败:', error.message);
    } finally {
      if (this.client) {
        await this.client.close();
      }
    }
  }

  /**
   * 连接到Auth服务
   */
  async connectToAuthService() {
    console.log('\n📡 连接到Auth服务...');
    
    try {
      this.client = ClientProxyFactory.create(AUTH_SERVICE_CONFIG);
      await this.client.connect();
      
      this.recordTest('Auth服务连接', true, '微服务连接成功');
    } catch (error) {
      this.recordTest('Auth服务连接', false, `连接失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 测试认证相关MessagePattern
   */
  async testAuthMessagePatterns() {
    console.log('\n🔐 测试认证相关MessagePattern');

    // 测试用户登录MessagePattern
    try {
      const loginResult = await this.client.send('login', {
        identifier: TEST_USER.username,
        password: TEST_USER.password,
        deviceInfo: {
          type: 'web',
          name: 'Test Device',
          fingerprint: 'test_fingerprint',
          userAgent: 'Test Agent',
          ipAddress: '127.0.0.1'
        }
      }).toPromise();

      if (loginResult.success) {
        this.testUserId = loginResult.data.user.id;
        this.testToken = loginResult.data.accessToken;
        this.recordTest('login MessagePattern', true, `登录成功，用户ID: ${this.testUserId}`);
      } else {
        this.recordTest('login MessagePattern', false, `登录失败: ${loginResult.error}`);
      }
    } catch (error) {
      this.recordTest('login MessagePattern', false, `调用异常: ${error.message}`);
    }

    // 测试Token验证MessagePattern
    if (this.testToken) {
      try {
        const verifyResult = await this.client.send('verifyToken', {
          token: this.testToken
        }).toPromise();

        this.recordTest('verifyToken MessagePattern', verifyResult.valid,
          `验证结果: ${verifyResult.valid ? '有效' : '无效'}`);
      } catch (error) {
        this.recordTest('verifyToken MessagePattern', false, `调用异常: ${error.message}`);
      }
    }

    // 测试权限检查MessagePattern
    if (this.testUserId) {
      try {
        const permissionResult = await this.client.send('checkPermission', {
          userId: this.testUserId,
          resource: 'test_resource',
          action: 'read'
        }).toPromise();

        this.recordTest('checkPermission MessagePattern', 
          typeof permissionResult.hasPermission === 'boolean',
          `权限检查结果: ${permissionResult.hasPermission}`);
      } catch (error) {
        this.recordTest('checkPermission MessagePattern', false, `调用异常: ${error.message}`);
      }
    }

    // 测试获取用户信息MessagePattern
    if (this.testUserId) {
      try {
        const userInfoResult = await this.client.send('getUserInfo', {
          userId: this.testUserId
        }).toPromise();

        this.recordTest('getUserInfo MessagePattern', !userInfoResult.error,
          `用户信息获取: ${userInfoResult.error ? '失败' : '成功'}`);
      } catch (error) {
        this.recordTest('getUserInfo MessagePattern', false, `调用异常: ${error.message}`);
      }
    }

    // 测试刷新Token MessagePattern
    if (this.testToken) {
      try {
        // 先获取refreshToken（这里简化处理，实际应该从登录结果中获取）
        const refreshResult = await this.client.send('refreshToken', {
          refreshToken: 'test_refresh_token' // 这里应该使用真实的refreshToken
        }).toPromise();

        this.recordTest('refreshToken MessagePattern', 
          refreshResult.success !== undefined,
          `刷新Token: ${refreshResult.success ? '成功' : refreshResult.error}`);
      } catch (error) {
        this.recordTest('refreshToken MessagePattern', false, `调用异常: ${error.message}`);
      }
    }

    // 测试登出MessagePattern
    if (this.testUserId && this.testToken) {
      try {
        const logoutResult = await this.client.send('logout', {
          userId: this.testUserId,
          token: this.testToken
        }).toPromise();

        this.recordTest('logout MessagePattern', logoutResult.success,
          `登出结果: ${logoutResult.success ? '成功' : logoutResult.error}`);
      } catch (error) {
        this.recordTest('logout MessagePattern', false, `调用异常: ${error.message}`);
      }
    }
  }

  /**
   * 测试用户管理MessagePattern
   */
  async testUserMessagePatterns() {
    console.log('\n👤 测试用户管理MessagePattern');

    if (!this.testUserId) {
      this.recordTest('用户管理MessagePattern测试', false, '缺少测试用户ID');
      return;
    }

    // 测试获取用户资料MessagePattern
    try {
      const profileResult = await this.client.send('getUserProfile', {
        userId: this.testUserId
      }).toPromise();

      this.recordTest('getUserProfile MessagePattern', profileResult.success,
        `用户资料获取: ${profileResult.success ? '成功' : profileResult.error}`);
    } catch (error) {
      this.recordTest('getUserProfile MessagePattern', false, `调用异常: ${error.message}`);
    }

    // 测试修改密码MessagePattern
    try {
      const changePasswordResult = await this.client.send('changePassword', {
        userId: this.testUserId,
        oldPassword: TEST_USER.password,
        newPassword: TEST_USER.password + '_new'
      }).toPromise();

      // 如果成功，改回原密码
      if (changePasswordResult.success) {
        await this.client.send('changePassword', {
          userId: this.testUserId,
          oldPassword: TEST_USER.password + '_new',
          newPassword: TEST_USER.password
        }).toPromise();
      }

      this.recordTest('changePassword MessagePattern', changePasswordResult.success,
        `密码修改: ${changePasswordResult.success ? '成功' : changePasswordResult.error}`);
    } catch (error) {
      this.recordTest('changePassword MessagePattern', false, `调用异常: ${error.message}`);
    }
  }

  /**
   * 测试API Key管理MessagePattern
   */
  async testApiKeyMessagePatterns() {
    console.log('\n🔑 测试API Key管理MessagePattern');

    let testApiKey = null;

    // 测试生成API Key MessagePattern
    if (this.testUserId) {
      try {
        const generateResult = await this.client.send('generateApiKey', {
          userId: this.testUserId,
          name: 'Test API Key',
          permissions: ['test:read', 'test:write']
        }).toPromise();

        if (generateResult.success) {
          testApiKey = generateResult.data.apiKey;
          this.recordTest('generateApiKey MessagePattern', true,
            `API Key生成成功: ${testApiKey.substring(0, 8)}***`);
        } else {
          this.recordTest('generateApiKey MessagePattern', false,
            `生成失败: ${generateResult.error}`);
        }
      } catch (error) {
        this.recordTest('generateApiKey MessagePattern', false, `调用异常: ${error.message}`);
      }
    }

    // 测试验证API Key MessagePattern
    if (testApiKey) {
      try {
        const validateResult = await this.client.send('validateApiKey', {
          apiKey: testApiKey,
          clientIp: '127.0.0.1',
          userAgent: 'Test Agent'
        }).toPromise();

        this.recordTest('validateApiKey MessagePattern', validateResult.success,
          `API Key验证: ${validateResult.success ? '成功' : validateResult.error}`);
      } catch (error) {
        this.recordTest('validateApiKey MessagePattern', false, `调用异常: ${error.message}`);
      }
    }

    // 测试撤销API Key MessagePattern
    if (testApiKey) {
      try {
        const revokeResult = await this.client.send('revokeApiKey', {
          apiKey: testApiKey
        }).toPromise();

        this.recordTest('revokeApiKey MessagePattern', revokeResult.success,
          `API Key撤销: ${revokeResult.success ? '成功' : revokeResult.error}`);
      } catch (error) {
        this.recordTest('revokeApiKey MessagePattern', false, `调用异常: ${error.message}`);
      }
    }

    // 测试列出API Keys MessagePattern
    if (this.testUserId) {
      try {
        const listResult = await this.client.send('listApiKeys', {
          userId: this.testUserId
        }).toPromise();

        this.recordTest('listApiKeys MessagePattern', listResult.success,
          `API Key列表: ${listResult.success ? '获取成功' : listResult.error}`);
      } catch (error) {
        this.recordTest('listApiKeys MessagePattern', false, `调用异常: ${error.message}`);
      }
    }
  }

  /**
   * 记录测试结果
   */
  recordTest(testName, success, details = '') {
    const result = {
      name: testName,
      success,
      details,
      timestamp: new Date().toISOString()
    };
    
    this.testResults.push(result);
    
    const status = success ? '✅' : '❌';
    console.log(`  ${status} ${testName}: ${details}`);
  }

  /**
   * 打印测试结果汇总
   */
  printTestResults() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 Auth服务MessagePattern验证结果汇总');
    console.log('='.repeat(60));

    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;

    console.log(`\n📈 总体统计:`);
    console.log(`  总测试数: ${totalTests}`);
    console.log(`  通过数: ${passedTests} ✅`);
    console.log(`  失败数: ${failedTests} ❌`);
    console.log(`  通过率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    if (failedTests > 0) {
      console.log(`\n❌ 失败的测试:`);
      this.testResults
        .filter(r => !r.success)
        .forEach(r => {
          console.log(`  - ${r.name}: ${r.details}`);
        });
    }

    console.log(`\n🎯 MessagePattern验证结果:`);
    if (passedTests >= totalTests * 0.8) {
      console.log('✅ Auth服务MessagePattern功能基本正常');
    } else {
      console.log('❌ Auth服务MessagePattern功能存在问题，需要修复');
    }
  }
}

// 运行测试
if (require.main === module) {
  const tester = new AuthMessagePatternTester();
  tester.runAllTests().catch(console.error);
}

module.exports = AuthMessagePatternTester;
