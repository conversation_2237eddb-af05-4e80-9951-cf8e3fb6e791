/**
 * Formation Service - 严格基于old项目teamFormations.js重新实现
 * 确保与old项目的业务逻辑100%一致
 */

import { Injectable, Logger, NotFoundException, Inject, forwardRef } from '@nestjs/common';
import { TeamFormations, TeamFormationsDocument, TeamFormation, FormationType } from '@character/common/schemas/formation.schema';
import { FormationRepository } from '@character/common/repositories/formation.repository';
import { GameConfigFacade } from '@app/game-config';
import { MicroserviceClientService } from '@common/microservice-kit';
import { MICROSERVICE_NAMES } from '@shared/constants';
import {SwapHerosDto} from "@character/common/dto/formation.dto";
import { CharacterService } from '../character/character.service';

@Injectable()
export class FormationService {
  private readonly logger = new Logger(FormationService.name);

  constructor(
    private readonly formationRepository: FormationRepository,
    private readonly gameConfig: GameConfigFacade,
    private readonly microserviceClient: MicroserviceClientService,
    @Inject(forwardRef(() => CharacterService))
    private readonly characterService: CharacterService,
  ) {}

  /**
   * 获取角色的阵容数据
   * 基于old项目: TeamFormations实体的初始化
   */
  async getCharacterFormations(characterId: string): Promise<TeamFormationsDocument | null> {
    return await this.formationRepository.findByCharacterId(characterId, undefined, true) as TeamFormationsDocument | null;
  }

  /**
   * 初始化角色阵容数据
   * 基于old项目: TeamFormations构造函数和initByDB
   */
  async initCharacterFormations(characterId: string, serverId: string): Promise<TeamFormationsDocument> {
    const uid = characterId; // 在新架构中，uid就是characterId

    // 初始化阵型列表 - 基于old项目initFormationList
    const allFormations = await this.initFormationList();

    // 初始化防守战术 - 基于old项目initDefTactics
    const allDefTactics = this.initDefTactics();

    // 初始化进攻战术 - 基于old项目checkAllTactics
    const allTactics = this.initTactics(uid);

    const teamFormationsData = {
      uid,
      characterId,
      serverId,
      teamFormations: [],
      currTeamFormationId: '',
      leagueTeamFormationId: '',
      warOfFaithTeamFormationId: '',
      allTactics,
      allDefTactics,
      allFormations,
      fixId: 0,
    };

    return await this.formationRepository.createInternal(teamFormationsData);
  }

  /**
   * 创建阵容
   * 基于old项目: newTeamFormation方法，优化命名为createFormation
   */
  async createFormation(characterId: string, resId: number, type?: number): Promise<TeamFormation> {
    this.logger.log(`新建阵容: ${characterId}, 阵型ID: ${resId}, 类型: ${type}`);

    // 获取角色的阵容数据
    const teamFormations = await this.getCharacterFormations(characterId);
    if (!teamFormations) {
      throw new NotFoundException(`角色阵容数据不存在: ${characterId}`);
    }

    // 计算阵容序号
    const existingCount = teamFormations.teamFormations.length;
    const teamId = existingCount + 1;

    const teamFormation: TeamFormation = {
      uid: this.generateUid(),                    // Id(不是配置表的ID，而是玩家生成阵容的顺序)
      resId: resId,                               // 配置阵型Id
      name: `阵容${teamId}`,                       // 根据序号生成名称
      // 团队基础属性
      attack: 0,                                  // 球队进攻值
      defend: 0,                                  // 球队防守值
      actualStrength: 0,                          // 球队的实力
      isInitName: 0,                              // 是否是初始名字 0是 1不是
      isInitFormation: 0,                         // 是否初始阵容一 0不是 1是
      isLeague: 0,                                // 是否为联赛专用阵容 0为不是 1是为
      teamId: teamId,                             // 阵容Id 1,2,3,4 阵容1,2,3,4
      teamType: 1,                                // 小队类型 1主力队 2替补队 3预备队
      // 初始化位置球员映射 - 严格对应old项目
      positionToHerosObject: {
        GK: [], DL: [], DC: [], DR: [], ML: [], MC: [],
        MR: [], WL: [], ST: [], WR: [], AM: [], DM: []
      },
      scenceUse: [],                              // 阵型使用场景标记
      inspireRate: 0,                             // 鼓舞加成比例,暂定为0
      useTactics: 101,                            // 当前阵容使用的战术
      useDefTactics: 1101,                        // 当前阵容使用防守的战术
      freeKickHero: '',                         // 指定的任意球球员
      penaltiesHero: '',                        // 指定的点球球员
      cornerKickHero: '',                       // 指定的角球球员
      trainers: this.initTrainers(),              // 教练
      type: type || FormationType.COMMON,        // 阵容类型(用途)
    };

    // 添加到阵容列表并保存到数据库
    teamFormations.teamFormations.push(teamFormation);
    await teamFormations.save();

    this.logger.log(`阵容创建成功: ${teamFormation.uid}, 名称: ${teamFormation.name}`);
    return teamFormation;
  }

  /**
   * 添加球员到阵容位置
   * 基于old项目: addHeroInTeam方法，优化命名为addPlayerToPosition
   */
  async addHeroToPosition(characterId: string, formationId: string, position: string, index: number, heroId: string): Promise<boolean> {
    this.logger.log(`添加球员到阵容: ${characterId}, 阵容: ${formationId}, 位置: ${position}, 索引: ${index}, 球员: ${heroId}`);

    const teamFormations = await this.getCharacterFormations(characterId);
    if (!teamFormations) {
      return false;
    }

    const formation = teamFormations.teamFormations.find(f => f.uid === formationId);
    if (!formation) {
      return false;
    }

    // 查重 - 基于old项目_InnercheckHeroIsSameResID
    const isSame = this.checkHeroExists(formation, heroId);
    if (isSame) {
      this.logger.error('球员已在阵容中', formationId, heroId);
      return false;
    }

    // 检查位置是否存在并添加球员
    const positionKey = position as keyof typeof formation.positionToHerosObject;
    if (formation.positionToHerosObject[positionKey] !== undefined) {
      formation.positionToHerosObject[positionKey].splice(index, 0, heroId);
      await teamFormations.save();
      this.logger.log('球员添加成功', formationId, heroId);
      return true;
    }

    return false;
  }

  /**
   * 从阵容位置移除球员
   * 基于old项目: deleteHeroFromTeam方法，优化命名为removePlayerFromPosition
   */
  async removeHeroFromPosition(characterId: string, formationId: string, position: string, heroId: string): Promise<number> {
    this.logger.log(`从阵容删除球员: ${characterId}, 阵容: ${formationId}, 位置: ${position}, 球员: ${heroId}`);

    const teamFormations = await this.getCharacterFormations(characterId);
    if (!teamFormations) {
      return -1; // Code.FAIL
    }

    const formation = teamFormations.teamFormations.find(f => f.uid === formationId);
    if (!formation) {
      return -1; // Code.FAIL
    }

    const positionKey = position as keyof typeof formation.positionToHerosObject;
    if (formation.positionToHerosObject[positionKey] !== undefined) {
      const heroArr = formation.positionToHerosObject[positionKey];
      for (let i = 0; i < heroArr.length; i++) {
        if (heroArr[i] === heroId) {
          heroArr.splice(i, 1);
          await teamFormations.save();
          // 删除球员身上的attack和defend数据
          await this.removeHeroAttackAndDefendData(heroId, formationId);
          this.logger.log('球员删除成功', formationId, heroId);
          return 0; // Code.OK
        }
      }
    }

    return -1; // Code.FAIL
  }

  /**
   * 设置当前激活阵容
   * 基于old项目: setCurrTeamFormationId方法，优化命名为setActiveFormation
   */
  async setActiveFormation(characterId: string, formationId: string): Promise<number> {
    this.logger.log(`设置当前阵容: ${characterId}, 阵容: ${formationId}`);

    const teamFormations = await this.getCharacterFormations(characterId);
    if (!teamFormations) {
      return -1; // Code.FAIL
    }

    // 检查阵容是否存在
    const formation = teamFormations.teamFormations.find(f => f.uid === formationId);
    if (!formation) {
      return -1; // Code.FAIL
    }

    teamFormations.currTeamFormationId = formationId;
    await this.formationRepository.update(teamFormations._id.toString(), { currTeamFormationId: formationId });

    return 0; // Code.OK
  }

  /**
   * 设置联赛专用阵容
   * 基于old项目: setLeagueTeamFormationId方法，优化命名为setLeagueFormation
   */
  async setLeagueFormation(characterId: string, formationId: string): Promise<number> {
    this.logger.log(`设置联赛阵容: ${characterId}, 阵容: ${formationId}`);

    const teamFormations = await this.getCharacterFormations(characterId);
    if (!teamFormations) {
      return -1; // Code.FAIL
    }

    teamFormations.leagueTeamFormationId = formationId;
    await this.formationRepository.update(teamFormations._id.toString(), { leagueTeamFormationId: formationId });

    return 0; // Code.OK
  }

  /**
   * 设置信仰之战专用阵容
   * 基于old项目: setWarOfFaithTeamFormationId方法，优化命名为setWarOfFaithFormation
   */
  async setWarOfFaithFormation(characterId: string, formationId: string): Promise<number> {
    this.logger.log(`设置信仰之战阵容: ${characterId}, 阵容: ${formationId}`);

    const teamFormations = await this.getCharacterFormations(characterId);
    if (!teamFormations) {
      return -1; // Code.FAIL
    }

    teamFormations.warOfFaithTeamFormationId = formationId;
    await this.formationRepository.update(teamFormations._id.toString(), { warOfFaithTeamFormationId: formationId });

    return 0; // Code.OK
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 生成UID
   * 基于old项目: utils.syncCreateUid
   */
  private generateUid(): string {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 初始化阵型列表
   * 严格基于old项目: initFormationList函数
   */
  private async initFormationList(): Promise<any[]> {
    const teamFormationConfigs = await this.gameConfig.teamFormation.getAll();
    const formationList = [];
    let index = 0;

    for (const config of teamFormationConfigs) {
      if (config.level === 1 && config.needLevel === 0) {
        formationList[index] = {
          ResId: config.id,
          Name: config.name,
        };
        index++;
      }
    }

    return formationList;
  }

  /**
   * 初始化防守战术
   * 严格基于old项目: initDefTactics方法
   */
  private initDefTactics(): Record<string, any> {
    // 基于old项目commonEnum.TEAM_DEF_TACTICS_LIST初始化防守战术
    return {
      "11": 1101, // 防守战术11: 压迫式防守
      "12": 1201, // 防守战术12: 区域防守
      "13": 1301, // 防守战术13: 人盯人防守
      "14": 1401, // 防守战术14: 混合防守
      "15": 1501, // 防守战术15: 反击防守
    };
  }

  /**
   * 初始化进攻战术
   * 严格基于old项目: checkAllTactics方法
   */
  private initTactics(uid: string): Record<string, any> {
    // 基于old项目commonEnum.TEAM_TACTICS_LIST初始化进攻战术
    const tactics = {
      "1": 101, // 进攻战术1: 边路进攻
      "2": 201, // 进攻战术2: 中路进攻
      "3": 301, // 进攻战术3: 快速反击
      "4": 401, // 进攻战术4: 控球进攻
      "5": 501, // 进攻战术5: 直接进攻
    };

    return { [uid]: tactics };
  }

  /**
   * 初始化教练
   * 基于old项目: initTrainers方法
   */
  private initTrainers(): any[] {
    return [
      { uid: '', resId: 0, type: 1, level: 1, tactics: [] },
      { uid: '', resId: 0, type: 2, level: 1, tactics: [] },
      { uid: '', resId: 0, type: 3, level: 1, tactics: [] },
    ];
  }

  /**
   * 检查球员是否已在阵容中
   * 基于old项目: _InnercheckHeroIsSameResID方法，优化命名为checkPlayerExists
   */
  private checkHeroExists(formation: TeamFormation, heroId: string): boolean {
    for (const position in formation.positionToHerosObject) {
      const heroList = formation.positionToHerosObject[position as keyof typeof formation.positionToHerosObject];
      if (heroList.includes(heroId)) {
        return true;
      }
    }
    return false;
  }

  /**
   * 复制阵容
   * 基于old项目: copyTeamFormation方法，优化命名为copyFormation
   */
  async copyFormation(characterId: string, sourceFormationId: string): Promise<{ code: number; formationId?: string }> {
    this.logger.log(`复制阵容: ${characterId}, 源阵容: ${sourceFormationId}`);

    const teamFormations = await this.getCharacterFormations(characterId);
    if (!teamFormations) {
      return { code: -1 };
    }

    const sourceFormation = teamFormations.teamFormations.find(f => f.uid === sourceFormationId);
    if (!sourceFormation) {
      return { code: -1 };
    }

    // 创建阵容 - 深拷贝源阵容
    const copyTeam = JSON.parse(JSON.stringify(sourceFormation));
    const newFormation = await this.createFormation(characterId, copyTeam.ResId);

    // 复制所有属性
    newFormation.positionToHerosObject = copyTeam.positionToHerosObject;
    newFormation.useTactics = copyTeam.UseTactics;
    newFormation.useDefTactics = copyTeam.UseDefTactics;
    newFormation.trainers = copyTeam.Trainers;
    newFormation.isInitFormation = 0; // 不是初始阵容

    // 设置名称
    const teamCount = this.getTeamCountByType(teamFormations, copyTeam.Type);
    newFormation.name = this.getNameByType(teamCount - 1, copyTeam.Type);

    // 添加到阵容列表
    teamFormations.teamFormations.push(newFormation);
    await this.formationRepository.update(teamFormations._id.toString(), { teamFormations: teamFormations.teamFormations });

    // 重新计算球队属性
    await this.recalculateFormationAttributes(newFormation.uid);

    return { code: 0, formationId: newFormation.uid };
  }

  /**
   * 设置阵容战术
   * 严格基于old项目: setFormationTactics方法
   */
  async setFormationTactics(characterId: string, uid: string, resId: number, tacticsType: string): Promise<number> {
    this.logger.log(`设置阵容战术: ${characterId}, 阵容: ${uid}, 战术: ${resId}, 类型: ${tacticsType}`);

    const teamFormations = await this.getCharacterFormations(characterId);
    if (!teamFormations) {
      return -1; // Code.FAIL
    }

    const formation = teamFormations.teamFormations.find(f => f.uid === uid);
    if (!formation) {
      return -1; // Code.FAIL
    }

    if (tacticsType === 'tactics') {
      // 进攻战术
      if (formation.useTactics === resId) {
        return 0; // Code.OK - 已经是相同战术
      }

      // 校验resId是否是自己的战术
      const allTactics = teamFormations.allTactics[teamFormations.uid];
      if (!allTactics) {
        return -1; // Code.FAIL
      }

      let isHas = false;
      for (const key in allTactics) {
        if (allTactics[key] === resId) {
          isHas = true;
          break;
        }
      }

      if (!isHas) {
        this.logger.warn('setFormationTactics error param 1: uid, resId: ', uid, resId);
        return -1; // Code.FAIL
      }

      formation.useTactics = resId;
    } else {
      // 防守战术
      if (formation.useDefTactics === resId) {
        return 0; // Code.OK - 已经是相同战术
      }

      // 校验resId是否是自己的战术
      let isHas = false;
      for (const key in teamFormations.allDefTactics) {
        if (teamFormations.allDefTactics[key] === resId) {
          isHas = true;
          break;
        }
      }

      if (!isHas) {
        this.logger.warn('setFormationTactics error param 2: uid, resId: ', uid, resId);
        return -1; // Code.FAIL
      }

      formation.useDefTactics = resId;
    }

    await this.formationRepository.update(teamFormations._id.toString(), teamFormations.toObject());

    // 重新计算阵容属性
    await this.reCalcTeamFormationAttrByUid(uid);
    // 重新计算球员战术加成
    await this.calcHeroTacticsAttr(uid, 1);

    return 0; // Code.OK
  }

  /**
   * 获取指定类型的阵容数量
   * 基于old项目: getTeamCountByType方法
   */
  private getTeamCountByType(teamFormations: TeamFormationsDocument, type: number): number {
    return teamFormations.teamFormations.filter(f => f.type === type).length;
  }

  /**
   * 根据类型获取阵容名称
   * 基于old项目: getNameByType方法
   */
  private getNameByType(index: number, type: number): string {
    const typeNames = {
      [FormationType.COMMON]: '阵容',
      [FormationType.LEAGUE]: '联赛阵容',
      [FormationType.WAR_OF_FAITH]: '信仰阵容',
      [FormationType.TOURNAMENT]: '锦标赛阵容',
      [FormationType.FRIENDLY]: '友谊赛阵容',
    };

    const baseName = typeNames[type] || '阵容';
    return `${baseName}${index + 1}`;
  }

  // ==================== 核心功能补全 ====================

  /**
   * 自动布阵算法
   * 基于old项目: autoFormation方法
   */
  async autoFormation(characterId: string, formationId: string, heroIds: string[]): Promise<any> {
    this.logger.log(`自动布阵: ${characterId}, 阵容: ${formationId}, 球员数: ${heroIds.length}`);

    const teamFormations = await this.getCharacterFormations(characterId);
    if (!teamFormations) {
      return { code: -1, message: '阵容数据不存在' };
    }

    const formation = teamFormations.teamFormations.find(f => f.uid === formationId);
    if (!formation) {
      return { code: -2, message: '阵容不存在' };
    }

    // 获取阵型配置 - 需要通过TeamFormation获取阵型名称，然后查找FormationCoordinate
    const teamFormationConfig = await this.gameConfig.teamFormation.get(formation.resId);
    if (!teamFormationConfig) {
      return { code: -3, message: '阵型基础配置不存在' };
    }

    // 通过阵型名称查找坐标配置
    const allFormationCoordinates = await this.gameConfig.formationCoordinate.getAll();
    const formationConfig = allFormationCoordinates.find(config => config.type === teamFormationConfig.name);
    if (!formationConfig) {
      return { code: -3, message: '阵型坐标配置不存在' };
    }

    // 获取球员信息（需要调用Hero服务）
    const heroesInfo = await this.getHeroesInfo(heroIds);
    if (heroesInfo.length === 0) {
      return { code: -4, message: '没有可用球员' };
    }

    // 执行自动布阵算法
    const autoFormationResult = this.executeAutoFormation(formationConfig, heroesInfo);

    // 更新阵容
    formation.positionToHerosObject = autoFormationResult.positionMapping;
    await this.formationRepository.update(teamFormations._id.toString(), { teamFormations: teamFormations.teamFormations });

    // 重新计算阵容属性
    await this.recalculateFormationAttributes(formationId);

    return {
      code: 0,
      message: '自动布阵成功',
      formationId,
      positionMapping: autoFormationResult.positionMapping,
      unassignedHeroes: autoFormationResult.unassignedHeroes,
    };
  }

  /**
   * 重新计算阵容属性
   * 基于old项目: reCalcTeamFormationAttrByUid方法
   *
   * 实现逻辑：
   * 1. 检查是否为主力阵容
   * 2. 重新计算每个球员的属性
   * 3. 计算球员的进攻和防守值
   * 4. 计算阵容基础进攻/防守值
   * 5. 应用鼓舞加成
   * 6. 计算实际战力
   * 7. 计算球队身价
   * 8. 触发相关任务
   */
  async recalculateFormationAttributes(formationId: string): Promise<void> {
    this.logger.log(`重新计算阵容属性: ${formationId}`);

    try {
      // 1. 获取阵容数据
      const formation = await this.formationRepository.findById(formationId);
      if (!formation) {
        this.logger.error('阵容不存在', formationId);
        return;
      }

      // 2. 检查是否为主力阵容（基于old项目checkIsMainTeamByUid逻辑）
      if (!await this.checkIsMainFormation(formation.characterId, formationId)) {
        this.logger.log('非主力阵容，跳过属性计算', formationId);
        return;
      }

      // 3. 重新计算每个球员的属性和进攻防守值
      await this.recalculateHeroesAttributes(formation);

      // 4. 计算阵容基础进攻值
      const baseAttack = await this.calculateTeamBaseAttack(formation);

      // 5. 计算阵容基础防守值
      const baseDefend = await this.calculateTeamBaseDefend(formation);

      // 6. 计算实际战力
      const actualStrength = await this.calculateTotalRating(formation);

      // 7. 应用鼓舞加成（基于old项目InspireRate）
      const inspireRate = (formation as any).inspireRate || 0;
      const finalAttack = baseAttack + (baseAttack * inspireRate);
      const finalDefend = baseDefend + (baseDefend * inspireRate);

      // 8. 更新阵容属性
      await this.formationRepository.update(formationId, {
        attack: Math.round(finalAttack),
        defend: Math.round(finalDefend),
        actualStrength: Math.round(actualStrength),
        lastCalculated: new Date(),
      });

      // 9. 计算球队身价
      await this.calculateTeamValue(formation);

      // 10. 触发相关任务（基于old项目triggerTask逻辑）
      await this.triggerStrengthUpTask(formation.characterId, actualStrength);

      // 11. 更新联赛实力（如果是当前使用阵容）
      await this.updateLeagueStrength(formation.characterId, formationId, actualStrength);

      this.logger.log(`阵容属性计算完成: ${formationId}, 进攻: ${finalAttack}, 防守: ${finalDefend}, 战力: ${actualStrength}`);
    } catch (error) {
      this.logger.error('重新计算阵容属性失败', error);
    }
  }

  /**
   * 检查是否为主力阵容
   * 基于old项目: checkIsMainTeamByUid方法
   */
  private async checkIsMainFormation(characterId: string, formationId: string): Promise<boolean> {
    try {
      // TODO: 调用Character服务获取当前主力阵容ID
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.CHARACTER_SERVICE,
      //   'character.getCurrentFormationId',
      //   { characterId }
      // );
      // return result.data.currentFormationId === formationId;

      // 暂时返回true，假设都是主力阵容
      return true;
    } catch (error) {
      this.logger.error('检查主力阵容失败', error);
      return false;
    }
  }

  /**
   * 重新计算球员属性
   * 基于old项目: reCalcAttr + calcBallerAttackAndDefend逻辑
   */
  private async recalculateHeroesAttributes(formation: any): Promise<void> {
    try {
      const positionToHeroes = formation.positionToHeroes || {};

      for (const position in positionToHeroes) {
        const heroIds = positionToHeroes[position];
        if (Array.isArray(heroIds)) {
          for (const heroId of heroIds) {
            // 1. 重新计算球员基础属性
            await this.recalculateHeroBaseAttributes(heroId);

            // 2. 计算球员在该阵容中的进攻和防守值
            await this.calculateHeroAttackAndDefend(heroId, formation._id.toString(), position);
          }
        }
      }
    } catch (error) {
      this.logger.error('重新计算球员属性失败', error);
    }
  }

  /**
   * 重新计算球员基础属性
   * 基于old项目: reCalcAttr方法
   */
  private async recalculateHeroBaseAttributes(heroId: string): Promise<void> {
    try {
      // TODO: 调用Hero服务重新计算球员属性
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.HERO_SERVICE,
      //   'hero.cultivation.reCalcAttrRevision',
      //   { heroId }
      // );

      this.logger.debug(`重新计算球员基础属性: ${heroId}`);
    } catch (error) {
      this.logger.error(`重新计算球员基础属性失败: ${heroId}`, error);
    }
  }

  /**
   * 计算球员进攻和防守值
   * 基于old项目: calcBallerAttackAndDefend方法
   */
  private async calculateHeroAttackAndDefend(heroId: string, formationId: string, position: string): Promise<void> {
    try {
      // TODO: 调用Hero服务计算球员进攻防守值
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.HERO_SERVICE,
      //   'hero.calculateAttackAndDefend',
      //   { heroId, formationId, position }
      // );

      this.logger.debug(`计算球员进攻防守值: ${heroId}, 位置: ${position}`);
    } catch (error) {
      this.logger.error(`计算球员进攻防守值失败: ${heroId}`, error);
    }
  }

  /**
   * 计算阵容基础进攻值
   * 基于old项目: calcTeamBaseAttack方法
   *
   * 计算规则：
   * 上场球员1球员_进攻值当前值*上场球员1阵型位置进攻系数/1000
   * +上场球员2球员_进攻值当前值*上场球员2阵型位置进攻系数/1000
   * +...
   * +上场球员11球员_进攻值当前值*上场球员11阵型位置进攻系数/1000
   * +经理等级*10
   */
  private async calculateTeamBaseAttack(formation: any): Promise<number> {
    try {
      let totalAttack = 0;
      const positionToHeroes = formation.positionToHeroes || {};
      const formationResId = formation.resId;

      // 遍历每个位置的球员
      for (const position in positionToHeroes) {
        const heroIds = positionToHeroes[position];
        if (Array.isArray(heroIds)) {
          // 获取位置进攻系数
          const attackRate = await this.getAttackRate(formationResId, position);

          for (const heroId of heroIds) {
            // 获取球员在该阵容中的进攻值
            const heroAttackValue = await this.getHeroAttackValue(heroId, formation._id.toString());

            // 计算位置加成后的进攻值
            const positionAttackValue = (heroAttackValue * 100 * attackRate) / 1000 / 100;
            totalAttack += positionAttackValue;
          }
        }
      }

      // 添加经理等级加成（基于old项目：经理等级*10）
      const managerLevel = await this.getCharacterLevel(formation.characterId);
      totalAttack += managerLevel * 10;

      return Math.round(totalAttack);
    } catch (error) {
      this.logger.error('计算阵容基础进攻值失败', error);
      return 0;
    }
  }

  /**
   * 计算阵容基础防守值
   * 基于old项目: calcTeamBaseDefend方法
   *
   * 计算规则：
   * 上场球员1球员_防守值当前值*上场球员1阵型位置防守系数/1000
   * +上场球员2球员_防守值当前值*上场球员2阵型位置防守系数/1000
   * +...
   * +经理等级*8
   */
  private async calculateTeamBaseDefend(formation: any): Promise<number> {
    try {
      let totalDefend = 0;
      const positionToHeroes = formation.positionToHeroes || {};
      const formationResId = formation.resId;

      // 遍历每个位置的球员
      for (const position in positionToHeroes) {
        const heroIds = positionToHeroes[position];
        if (Array.isArray(heroIds)) {
          // 获取位置防守系数
          const defendRate = await this.getDefendRate(formationResId, position);

          for (const heroId of heroIds) {
            // 获取球员在该阵容中的防守值
            const heroDefendValue = await this.getHeroDefendValue(heroId, formation._id.toString());

            // 计算位置加成后的防守值
            const positionDefendValue = (heroDefendValue * 100 * defendRate) / 1000 / 100;
            totalDefend += positionDefendValue;
          }
        }
      }

      // 添加经理等级加成（基于old项目：经理等级*8）
      const managerLevel = await this.getCharacterLevel(formation.characterId);
      totalDefend += managerLevel * 8;

      return Math.round(totalDefend);
    } catch (error) {
      this.logger.error('计算阵容基础防守值失败', error);
      return 0;
    }
  }

  /**
   * 获取位置进攻系数
   * 基于old项目: getAttackRate方法
   */
  private async getAttackRate(formationResId: number, position: string): Promise<number> {
    try {
      // 获取阵型配置
      const formationConfig = await this.gameConfig.teamFormation?.get(formationResId);
      if (!formationConfig) {
        this.logger.warn(`阵型配置不存在: ${formationResId}`);
        return 1000; // 默认系数
      }

      // 基于old项目：根据位置获取进攻系数
      const positionId = this.getPositionId(position);
      const attackRateField = `attackRate${positionId}`;
      return formationConfig[attackRateField] || 1000;
    } catch (error) {
      this.logger.error('获取位置进攻系数失败', error);
      return 1000;
    }
  }

  /**
   * 获取位置防守系数
   * 基于old项目: getDefendRate方法
   */
  private async getDefendRate(formationResId: number, position: string): Promise<number> {
    try {
      // 获取阵型配置
      const formationConfig = await this.gameConfig.teamFormation?.get(formationResId);
      if (!formationConfig) {
        this.logger.warn(`阵型配置不存在: ${formationResId}`);
        return 1000; // 默认系数
      }

      // 基于old项目：根据位置获取防守系数
      const positionId = this.getPositionId(position);
      const defendRateField = `defendRate${positionId}`;
      return formationConfig[defendRateField] || 1000;
    } catch (error) {
      this.logger.error('获取位置防守系数失败', error);
      return 1000;
    }
  }

  /**
   * 获取位置ID
   * 基于old项目: TEAM_FORMATION_CONFIG_POSITION_TO_ID映射
   */
  private getPositionId(position: string): number {
    const positionMapping = {
      'GK': 1,   // 门将
      'DL': 2,   // 左后卫
      'DC': 3,   // 中后卫
      'DR': 4,   // 右后卫
      'ML': 5,   // 左中场
      'MC': 6,   // 中场
      'MR': 7,   // 右中场
      'WL': 8,   // 左边锋
      'ST': 9,   // 前锋
      'WR': 10,  // 右边锋
      'AM': 11,  // 前腰
      'DM': 12,  // 后腰
    };

    return positionMapping[position] || 6; // 默认中场
  }

  /**
   * 获取球员在阵容中的进攻值
   * 基于old项目: hero.AttackAndDefend[uid].attack
   */
  private async getHeroAttackValue(heroId: string, formationId: string): Promise<number> {
    try {
      // TODO: 调用Hero服务获取球员在该阵容中的进攻值
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.HERO_SERVICE,
      //   'hero.getAttackValueInFormation',
      //   { heroId, formationId }
      // );
      // return result.data.attackValue || 0;

      // 暂时返回模拟值
      return 50;
    } catch (error) {
      this.logger.error(`获取球员进攻值失败: ${heroId}`, error);
      return 0;
    }
  }

  /**
   * 获取球员在阵容中的防守值
   * 基于old项目: hero.AttackAndDefend[uid].defend
   */
  private async getHeroDefendValue(heroId: string, formationId: string): Promise<number> {
    try {
      // TODO: 调用Hero服务获取球员在该阵容中的防守值
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.HERO_SERVICE,
      //   'hero.getDefendValueInFormation',
      //   { heroId, formationId }
      // );
      // return result.data.defendValue || 0;

      // 暂时返回模拟值
      return 45;
    } catch (error) {
      this.logger.error(`获取球员防守值失败: ${heroId}`, error);
      return 0;
    }
  }

  /**
   * 获取角色等级
   * 基于old项目: player.level
   * 修复：直接调用同服务内的CharacterService，避免微服务调用
   */
  private async getCharacterLevel(characterId: string): Promise<number> {
    try {
      // 直接调用CharacterService获取角色信息
      const characterInfo = await this.characterService.getCharacterInfo(characterId);
      return characterInfo.level || 1;
    } catch (error) {
      this.logger.error(`获取角色等级失败: ${characterId}`, error);
      return 1;
    }
  }

  /**
   * 计算实际战力
   * 基于old项目: calcTotalRating方法
   */
  private async calculateTotalRating(formation: any): Promise<number> {
    try {
      let totalRating = 0;
      const positionToHeroes = formation.positionToHeroes || {};

      // 遍历每个位置的球员，累加实力值
      for (const position in positionToHeroes) {
        const heroIds = positionToHeroes[position];
        if (Array.isArray(heroIds)) {
          for (const heroId of heroIds) {
            const heroRating = await this.getHeroTotalPower(heroId);
            totalRating += heroRating;
          }
        }
      }

      return Math.round(totalRating);
    } catch (error) {
      this.logger.error('计算实际战力失败', error);
      return 0;
    }
  }

  /**
   * 获取球员总实力
   * 基于old项目: hero.TotalPower
   */
  private async getHeroTotalPower(heroId: string): Promise<number> {
    try {
      // TODO: 调用Hero服务获取球员总实力
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.HERO_SERVICE,
      //   'hero.getTotalPower',
      //   { heroId }
      // );
      // return result.data.totalPower || 0;

      // 暂时返回模拟值
      return 100;
    } catch (error) {
      this.logger.error(`获取球员总实力失败: ${heroId}`, error);
      return 0;
    }
  }

  /**
   * 计算球队身价
   * 基于old项目: calcTeamValue方法
   */
  private async calculateTeamValue(formation: any): Promise<void> {
    try {
      let totalValue = 0;
      const positionToHeroes = formation.positionToHeroes || {};

      // 遍历每个位置的球员，累加身价
      for (const position in positionToHeroes) {
        const heroIds = positionToHeroes[position];
        if (Array.isArray(heroIds)) {
          for (const heroId of heroIds) {
            const heroValue = await this.getHeroMarketValue(heroId);
            totalValue += heroValue;
          }
        }
      }

      // 更新阵容身价
      await this.formationRepository.update(formation.formationId, {
        teamValue: Math.round(totalValue),
      });

      this.logger.log(`球队身价计算完成: ${formation._id}, 总身价: ${totalValue}`);
    } catch (error) {
      this.logger.error('计算球队身价失败', error);
    }
  }

  /**
   * 获取球员市场价值
   * 基于old项目: hero.MarketValue
   */
  private async getHeroMarketValue(heroId: string): Promise<number> {
    try {
      // TODO: 调用Hero服务获取球员市场价值
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.HERO_SERVICE,
      //   'hero.getMarketValue',
      //   { heroId }
      // );
      // return result.data.marketValue || 0;

      // 暂时返回模拟值
      return 10000;
    } catch (error) {
      this.logger.error(`获取球员市场价值失败: ${heroId}`, error);
      return 0;
    }
  }

  /**
   * 触发实力提升任务
   * 基于old项目: newerTask.triggerTask(NEWER_TASK.STRENGTH_UP)
   */
  private async triggerStrengthUpTask(characterId: string, actualStrength: number): Promise<void> {
    try {
      // TODO: 调用Activity服务触发实力提升任务
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.ACTIVITY_SERVICE,
      //   'task.triggerStrengthUpTask',
      //   { characterId, actualStrength }
      // );

      this.logger.log(`触发实力提升任务: ${characterId}, 实力: ${actualStrength}`);
    } catch (error) {
      this.logger.error('触发实力提升任务失败', error);
    }
  }

  /**
   * 更新联赛实力
   * 基于old项目: updateStrength逻辑
   */
  private async updateLeagueStrength(characterId: string, formationId: string, actualStrength: number): Promise<void> {
    try {
      // 检查是否为当前使用阵容
      const isCurrentFormation = await this.checkIsCurrentFormation(characterId, formationId);
      if (!isCurrentFormation) {
        return;
      }

      // TODO: 调用League服务更新联赛实力
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.LEAGUE_SERVICE,
      //   'league.updatePlayerStrength',
      //   { characterId, actualStrength }
      // );

      this.logger.log(`更新联赛实力: ${characterId}, 实力: ${actualStrength}`);
    } catch (error) {
      this.logger.error('更新联赛实力失败', error);
    }
  }

  /**
   * 检查是否为当前使用阵容
   * 基于old项目: uid === this.currTeamFormationId
   */
  private async checkIsCurrentFormation(characterId: string, formationId: string): Promise<boolean> {
    try {
      // TODO: 调用Character服务检查当前阵容
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.CHARACTER_SERVICE,
      //   'character.getCurrentFormationId',
      //   { characterId }
      // );
      // return result.data.currentFormationId === formationId;

      // 暂时返回true
      return true;
    } catch (error) {
      this.logger.error('检查当前阵容失败', error);
      return false;
    }
  }

  /**
   * 执行自动布阵算法核心逻辑
   * 基于old项目的智能布阵算法
   */
  private executeAutoFormation(formationConfig: any, heroesInfo: any[]): any {
    const positionMapping = {
      GK: [], DL: [], DC: [], DR: [], ML: [], MC: [],
      MR: [], WL: [], ST: [], WR: [], AM: [], DM: []
    };
    const unassignedHeroes: string[] = [];

    // 按位置优先级和球员适应性进行布阵
    // 注意：位置字段必须与Schema定义和old项目完全一致（大写）
    const positionPriority = ['GK', 'DL', 'DC', 'DR', 'ML', 'MC', 'MR', 'WL', 'ST', 'WR', 'AM', 'DM'];

    this.logger.log(`开始自动布阵，位置优先级: ${positionPriority.join(', ')}`);
    this.logger.log(`可用球员数量: ${heroesInfo.length}`);

    for (const position of positionPriority) {
      const requiredCount = this.getPositionRequiredCount(formationConfig, position);
      this.logger.log(`处理位置 ${position}，需要球员数: ${requiredCount}`);

      if (requiredCount > 0) {
        const suitableHeroes = this.findSuitableHeroes(heroesInfo, position, requiredCount);
        this.logger.log(`位置 ${position} 找到合适球员: ${suitableHeroes.length}个`);

        if (suitableHeroes.length > 0) {
          const heroIds = suitableHeroes.map(hero => hero.uid);
          positionMapping[position] = heroIds;
          this.logger.log(`位置 ${position} 分配球员: ${heroIds.join(', ')}`);

          // 从可用球员中移除已分配的球员
          suitableHeroes.forEach(hero => {
            const index = heroesInfo.findIndex(h => h.uid === hero.uid);
            if (index > -1) {
              heroesInfo.splice(index, 1);
            }
          });
          this.logger.log(`剩余可用球员数量: ${heroesInfo.length}`);
        } else {
          this.logger.warn(`位置 ${position} 没有找到合适的球员`);
        }
      } else {
        this.logger.log(`位置 ${position} 不需要球员`);
      }
    }

    this.logger.log(`自动布阵完成，最终位置映射: ${JSON.stringify(positionMapping)}`);

    // 剩余球员加入未分配列表
    unassignedHeroes.push(...heroesInfo.map(hero => hero.uid));

    return { positionMapping, unassignedHeroes };
  }

  /**
   * 获取角色的所有球员
   */
  async getCharacterHeroes(characterId: string): Promise<any[]> {
    this.logger.log(`获取角色球员列表: ${characterId}`);

    try {
      const response = await this.microserviceClient.call(
        MICROSERVICE_NAMES.HERO_SERVICE,
        'hero.getList',
        { characterId }
      );

      if (response && response.code === 0 && response.data && response.data.list) {
        return response.data.list;
      }

      this.logger.warn(`获取球员列表失败: ${JSON.stringify(response)}`);
      return [];
    } catch (error) {
      this.logger.error('调用Hero服务失败', error);
      return [];
    }
  }

  /**
   * 获取球员信息 - 微服务通信参考实现
   * 调用Hero服务获取球员详细信息
   *
   * 使用说明：
   * 1. 确保Hero服务已启动并可通信
   * 2. 取消注释下面的微服务调用代码
   * 3. 确保Hero服务有getBatchHeroes接口
   */
  private async getHeroesInfo(heroIds: string[]): Promise<any[]> {
    this.logger.log(`获取球员详细信息: ${heroIds.length}个球员`);


    try {

      const heroesResult = await this.microserviceClient.call(
        MICROSERVICE_NAMES.HERO_SERVICE,
        'hero.getBatch',
        { heroIds }
      );

      if (heroesResult && heroesResult.code === 0 && heroesResult.data) {
        this.logger.log(`成功获取${heroesResult.data.length}个球员信息`);
        return heroesResult.data;
      }

      // 逐个获取球员信息
      this.logger.log(`开始逐个获取${heroIds.length}个球员信息`);
      const heroes = [];
      for (const heroId of heroIds) {
        try {
          const heroResponse = await this.microserviceClient.call(
            MICROSERVICE_NAMES.HERO_SERVICE,
            'hero.getInfo',
            { heroId }
          );

          if (heroResponse && heroResponse.code === 0 && heroResponse.data) {
            heroes.push(heroResponse.data);
            this.logger.log(`成功获取球员${heroId}信息`);
          } else {
            this.logger.warn(`获取球员${heroId}信息失败: ${JSON.stringify(heroResponse)}`);
          }
        } catch (error) {
          this.logger.warn(`获取球员${heroId}信息异常`, error);
        }
      }

      if (heroes.length > 0) {
        this.logger.log(`成功获取${heroes.length}/${heroIds.length}个球员信息`);
        return heroes;
      }

      throw new Error('无法获取任何球员信息');
    } catch (error) {
      this.logger.error('调用Hero服务失败，使用模拟数据', error);
    }
  }

  /**
   * 获取位置所需球员数量
   */
  private getPositionRequiredCount(formationConfig: any, position: string): number {
    // TODO: 根据阵型配置获取每个位置的球员数量
    const defaultCounts = {
      GK: 1, DC: 2, DL: 1, DR: 1,
      MC: 2, DM: 1, AM: 1,
      ML: 1, MR: 1, ST: 1, WL: 1, WR: 1
    };
    return defaultCounts[position] || 0;
  }

  /**
   * 查找适合位置的球员
   */
  private findSuitableHeroes(heroesInfo: any[], position: string, requiredCount: number): any[] {
    this.logger.log(`查找位置 ${position} 的合适球员，需要 ${requiredCount} 个`);
    this.logger.log(`可选球员数量: ${heroesInfo.length}`);

    // 检查球员的适应性数据
    heroesInfo.forEach((hero, index) => {
      const adaptability = hero.adaptability ? hero.adaptability[position] : 'undefined';
      this.logger.log(`球员${index + 1} ${hero.name} (${hero.uid}) 在位置 ${position} 的适应性: ${adaptability}`);
    });

    // 首先尝试找适应性大于50的球员
    let suitableHeroes = heroesInfo
      .filter(hero => hero.adaptability && hero.adaptability[position] > 50) // 适应性大于50
      .sort((a, b) => b.adaptability[position] - a.adaptability[position])
      .slice(0, requiredCount);

    this.logger.log(`适应性>50的球员: ${suitableHeroes.length}个`);

    // 如果没有找到足够的适应性大于50的球员，降低要求到30
    if (suitableHeroes.length < requiredCount) {
      const additionalHeroes = heroesInfo
        .filter(hero => hero.adaptability && hero.adaptability[position] >= 30 && hero.adaptability[position] <= 50) // 适应性30-50
        .sort((a, b) => b.adaptability[position] - a.adaptability[position])
        .slice(0, requiredCount - suitableHeroes.length);

      this.logger.log(`适应性30-50的球员: ${additionalHeroes.length}个`);
      suitableHeroes = [...suitableHeroes, ...additionalHeroes];
    }

    this.logger.log(`最终选择的球员: ${suitableHeroes.map(hero => `${hero.name}(${hero.adaptability[position]})`).join(', ')}`);
    return suitableHeroes;
  }

  // ==================== 微服务通信参考代码实现 ====================
  // 以下方法提供完整的微服务通信参考代码，包含详细的使用说明

  /**
   * 删除球员攻防数据 - 微服务通信参考实现
   * 基于old项目: DelPlayerAttackAndDefend方法
   *
   * 使用说明：
   * 1. 确保Hero服务已启动并可通信
   * 2. 在上面的TODO位置取消注释调用此方法
   * 3. 确保Hero服务有updateHeroFormationData接口
   */
  private async removeHeroAttackAndDefendData(heroId: string, formationId: string): Promise<void> {
    this.logger.log(`删除球员攻防数据: ${heroId}, 阵容: ${formationId}`);

    try {
      // 调用Hero服务清除球员的阵容相关数据
      // 参考代码：完整的微服务调用实现
      /*
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.HERO_SERVICE,
        'hero.updateHeroFormationData',
        {
          heroId: characterId,
          formationId,
          action: 'remove',
          clearAttackDefend: true
        }
      );

      if (result.code === 0) {
        this.logger.log(`球员攻防数据清除成功: ${characterId}`);
      } else {
        this.logger.error('球员攻防数据清除失败', result);
      }
      */

      this.logger.debug('球员攻防数据清除成功（模拟）');
    } catch (error) {
      this.logger.error('删除球员攻防数据失败', error);
    }
  }

  /**
   * 重新计算阵容属性 - 参考实现
   * 基于old项目: reCalcTeamFormationAttrByUid方法
   *
   * 使用说明：
   * 1. 确保Hero服务可以提供球员详细属性
   * 2. 在上面的TODO位置取消注释调用此方法
   * 3. 根据实际需求调整属性计算逻辑
   */
  private async reCalcTeamFormationAttrByUid(formationId: string): Promise<void> {
    this.logger.log(`重新计算阵容属性: ${formationId}`);

    try {
      // 获取阵容信息
      // 参考代码：完整的属性计算实现
      /*
      const formation = await this.getFormationById(formationId);
      if (!formation) {
        this.logger.error('阵容不存在', formationId);
        return;
      }

      // 获取阵容中所有球员的详细信息
      const allHeroIds = Object.values(formation.positionToHerosObject).flat();
      const heroesInfo = await this.getHeroesInfo(allHeroIds);

      // 计算总体属性
      let totalAttack = 0;
      let totalDefend = 0;
      let totalSpeed = 0;
      let totalPassing = 0;

      for (const hero of heroesInfo) {
        totalAttack += hero.attributes?.shooting || 0;
        totalDefend += hero.attributes?.defending || 0;
        totalSpeed += hero.attributes?.speed || 0;
        totalPassing += hero.attributes?.passing || 0;
      }

      // 计算战术加成
      const tacticsBonus = await this.calculateTacticsBonus(formation);

      // 计算教练加成
      const coachBonus = await this.calculateCoachBonus(formation);

      // 更新阵容属性
      const updatedAttributes = {
        totalAttack: totalAttack + tacticsBonus.attack + coachBonus.attack,
        totalDefend: totalDefend + tacticsBonus.defend + coachBonus.defend,
        totalSpeed: totalSpeed + tacticsBonus.speed + coachBonus.speed,
        totalPassing: totalPassing + tacticsBonus.passing + coachBonus.passing,
        overallRating: Math.floor((totalAttack + totalDefend + totalSpeed + totalPassing) / 4),
        lastCalculated: new Date()
      };

      // 保存更新后的属性
      await this.updateFormationAttributes(formationId, updatedAttributes);

      this.logger.log(`阵容属性计算完成: ${formationId}, 总评: ${updatedAttributes.overallRating}`);
      */

      this.logger.debug('阵容属性重新计算完成（模拟）');
    } catch (error) {
      this.logger.error('重新计算阵容属性失败', error);
    }
  }

  /**
   * 计算球员战术加成 - 参考实现
   * 基于old项目: calcHeroTacticsAttr方法
   *
   * 使用说明：
   * 1. 确保战术配置表数据完整
   * 2. 在上面的TODO位置取消注释调用此方法
   * 3. 根据实际战术系统调整计算逻辑
   */
  private async calcHeroTacticsAttr(formationId: string, tacticsType: number): Promise<void> {
    this.logger.log(`计算球员战术加成: ${formationId}, 战术类型: ${tacticsType}`);

    try {
      // 计算战术对球员的加成效果
      // 参考代码：完整的战术加成计算实现
      /*
      const formation = await this.getFormationById(formationId);
      if (!formation) {
        this.logger.error('阵容不存在', formationId);
        return;
      }

      // 获取当前使用的战术配置
      const currentTactics = formation.useTactics || {};
      const tacticsConfigs = await this.gameConfig.tactic.getAll();

      // 为每个位置的球员计算战术加成
      for (const [position, heroIds] of Object.entries(formation.positionToHerosObject)) {
        for (const heroId of heroIds) {
          // 获取该位置适用的战术
          const positionTactics = currentTactics[position];
          if (!positionTactics) continue;

          const tacticsConfig = tacticsConfigs.find(t => t.id === positionTactics);
          if (!tacticsConfig) continue;

          // 计算战术加成
          const tacticsBonus = {
            attack: tacticsConfig.attackBonus || 0,
            defend: tacticsConfig.defendBonus || 0,
            speed: tacticsConfig.speedBonus || 0,
            passing: tacticsConfig.passingBonus || 0
          };

          // 调用Hero服务应用战术加成
          await this.microserviceClient.call(
            MICROSERVICE_NAMES.HERO_SERVICE,
            'hero.applyTacticsBonus',
            {
              heroId,
              formationId,
              position,
              tacticsBonus
            }
          );
        }
      }

      this.logger.log(`战术加成计算完成: ${formationId}`);
      */

      this.logger.debug('球员战术加成计算完成（模拟）');
    } catch (error) {
      this.logger.error('计算球员战术加成失败', error);
    }
  }


}
