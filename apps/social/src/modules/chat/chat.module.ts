/**
 * 聊天模块
 * 处理游戏内聊天功能
 */

import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ChatController } from './chat.controller';
import { ChatService } from './chat.service';
import { ChatRepository } from '../../common/repositories/chat.repository';
import {
  ChatMessage,
  ChatMessageSchema,
  ChatChannel,
  ChatChannelSchema
} from '../../common/schemas/chat.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: ChatMessage.name, schema: ChatMessageSchema },
      { name: ChatChannel.name, schema: ChatChannelSchema },
    ]),
  ],
  controllers: [ChatController],
  providers: [ChatService, ChatRepository],
  exports: [ChatService, ChatRepository],
})
export class ChatModule {}
