import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { MICROSERVICE_NAMES } from '@shared/constants';
import { AppModule } from './app.module';

/**
 * 比赛系统微服务启动入口
 *
 * 服务信息：
 * - 服务名称: match
 * - 端口: 3005 (从环境变量获取)
 * - 协议: Redis (微服务间通信)
 * - HTTP端口: 用于健康检查和Swagger文档
 *
 * 启动步骤：
 * 1. 创建应用实例
 * 2. 获取配置服务
 * 3. 设置全局前缀
 * 4. 配置Swagger文档
 * 5. 启动HTTP服务器
 */
async function bootstrap() {
  // 1. 创建应用实例
  const app = await NestFactory.create(AppModule);
  const logger = new Logger('MatchService');

  // 2. 获取配置服务
  const configService = app.get(ConfigService);
  const port = configService.get<number>('MATCH_PORT') || 3005;
  const environment = configService.get<string>('NODE_ENV') || 'development';

  // 3. 设置全局前缀
  app.setGlobalPrefix('api');

  // 4. 配置Swagger文档
  if (environment !== 'production') {
    const config = new DocumentBuilder()
      .setTitle('足球经理比赛服务 API')
      .setDescription('比赛系统微服务API文档')
      .setVersion('1.0.0')
      .addTag('match', '比赛系统')
      .addTag('league', '联赛系统')
      .addTag('business', '商业赛系统')
      .addTag('trophy', '杯赛系统')
      .addTag('tournament', '锦标赛系统')
      .addTag('battle', '战斗引擎')
      .addTag('ranking', '排名系统')
      .addTag('health', '健康检查')
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('api/docs', app, document);

    logger.log(`📚 Swagger文档已启用: http://localhost:${port}/api/docs`);
  }

  // 5. 使用 ConfigService 获取微服务配置
  const microserviceConfig = configService.get('microserviceKit');
  const serviceConfig = microserviceConfig?.services[MICROSERVICE_NAMES.MATCH_SERVICE];

  if (serviceConfig) {
    const microserviceOptions = {
      transport: serviceConfig.transport,
      options: serviceConfig.options,
    };

    // 调试日志：显示微服务配置
    logger.log(`🔗 微服务传输层配置:`);
    logger.log(`📡 传输方式: ${serviceConfig.transport}`);
    logger.log(`🔌 Redis 主机: ${serviceConfig.options.host}`);
    logger.log(`🔌 Redis 端口: ${serviceConfig.options.port}`);
    logger.log(`🗄️  Redis 数据库: ${serviceConfig.options.db}`);
    logger.log(`🔑 Redis 密码: ${serviceConfig.options.password ? '***' : '未设置'}`);

    // 连接微服务传输层
    app.connectMicroservice(microserviceOptions);

    // 启动所有微服务
    await app.startAllMicroservices();
    logger.log(`🔗 微服务传输层已启动 (${serviceConfig.transport})`);
  } else {
    logger.error(`❌ 未找到微服务配置: ${MICROSERVICE_NAMES.MATCH_SERVICE}`);
    logger.error(`📋 可用配置: ${Object.keys(microserviceConfig?.services || {}).join(', ')}`);
  }

  // 6. 启动HTTP服务器
  await app.listen(port);

  logger.log(`🚀 比赛服务已启动`);
  logger.log(`📡 服务名称: ${MICROSERVICE_NAMES.MATCH_SERVICE}`);
  logger.log(`🌐 HTTP端口: ${port}`);
  logger.log(`🔧 运行环境: ${environment}`);
  logger.log(`📊 健康检查: http://localhost:${port}/api/health`);
}

bootstrap().catch((error) => {
  console.error('❌ 比赛服务启动失败:', error);
  process.exit(1);
});
